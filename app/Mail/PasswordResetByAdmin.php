<?php

namespace App\Mail;

use App\Models\Template;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email;

class PasswordResetByAdmin extends TenantAwareMailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $subject;

    public string $content;

    public string $text;

    public object $styles;

    public function __construct(
        public int $user_id,
        public string $encrypted_password
    ) {}

    public function build(): ?PasswordResetByAdmin
    {
        $user = User::find($this->user_id);

        if (is_null($user)) return null;

        $this->metadata['customer_id'] = $user->id;

        $template = $this->template();

        $mergedContent = $template->mergeWithUser($user);

        $this->subject = 'Your password was reset';
        $this->content = $mergedContent->getHTML();
        $this->text = $mergedContent->getText();
        $this->styles = $template->settings;

        $this->from(config('mail.from.address'), setting('farm_name'));
        $this->replyTo(setting('email_general'), setting('farm_name'));

        return $this->withSymfonyMessage(function (Email $m) {
            $headers = $m->getHeaders();
            $headers->addTextHeader('X-Mailgun-Variables', json_encode($this->metadata));
            $headers->addTextHeader('X-Mailgun-Tag', 'Order Confirmation');
        })
            ->view('emails.transactional-html')
            ->text('emails.transactional-text');
    }

    public function template(): Template
    {
        $html = view('emails.default.password-reset')
            ->with([
                'encrypted_password' => $this->encrypted_password
            ])
            ->render();

        $template = new Template;
        $template->title = 'Password Reset';
        $template->slug ='your-password-was-reset';
        $template->subject = 'Your Password was reset';
        $template->from_name = null;
        $template->from_email = null;
        $template->settings = [
            'backgroundColor' => '#FAFAFA',
            'fontFamily' => 'Arial',
            'track_links' => false,
            'campaign_name' => 'Your password was reset!',
            'campaign_content' => ''
        ];
        $template->body = $html;
        $template->preview = $html;
        $template->needs_published = false;
        $template->plain_text = view('emails.default.password-reset-plaintext')
            ->with([
                'encrypted_password' => $this->encrypted_password
            ])
            ->render();
        $template->group = 'user';
        $template->type = 'email';
        $template->created_at = now();
        $template->updated_at = now();

        return $template;
    }

    public function tags()
    {
        return ['email'];
    }
}
