<?php

namespace App\Jobs;

use App\Models\Address;
use App\Models\User;
use App\Services\SettingsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class BackFillUserAddresses implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 1;

    public function handle(): void
    {
        $country = app(SettingsService::class)->farmCountry();

        User::query()
            ->where(function ($query) {
                return $query
                    ->whereNotNull('street')
                    ->whereNot('street','')
                    ->whereNotNull('city')
                    ->whereNot('city','')
                    ->whereNotNull('state')
                    ->whereNot('state','')
                    ->whereNotNull('zip')
                    ->whereNot('zip','');
            })
            ->chunkById(100, function (Collection $user) use ($country) {
                $user->each(function (User $user) use ($country) {
                    $address = Address::firstOrCreate([
                        'street' => $user->street,
                        'city' => $user->city,
                        'state' => $user->state,
                        'postal_code' => $user->zip,
                        'country' => $country,
                    ]);

                    $user->addresses()->detach();

                    $user->addresses()->attach([$address->id => [
                        'name' => Str::limit( "{$user->street}, {$user->city}, {$user->state}, {$user->zip}"),
                        'street_2' => $user->street_2,
                        'is_default' => true,
                    ]]);
                });
            });
    }
}
