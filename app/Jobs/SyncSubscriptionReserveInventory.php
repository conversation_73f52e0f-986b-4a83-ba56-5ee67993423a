<?php

namespace App\Jobs;

use App\Services\GoogleSheetsService;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SyncSubscriptionReserveInventory implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(): void
    {
        try {
            $worksheet_contents = (new GoogleSheetsService())
                ->getWorksheetContents(
                    config('services.google.spreadsheet_id'),
                    config('services.google.worksheet_name')
                );
        } catch (\Exception $exception) {
            if (app()->isProduction()) {
                Bugsnag::notifyException($exception);
            }

            return;
        }

        $this->updateOtherInventory(
            $this->mapInventoryFieldsBySku($worksheet_contents)
        );
    }

    private function mapInventoryFieldsBySku(array $reserve_inventory): Collection
    {
        $collection = collect($reserve_inventory);
        $headers = $collection->first();

       return $collection->slice(1)->map(function ($item) use ($headers) {
            return array_combine($headers, $item);
        });
    }

    private function updateOtherInventory(Collection $insert_data): void
    {
        DB::transaction(function () use ($insert_data) {
            DB::statement('CREATE TEMPORARY TABLE IF NOT EXISTS temp_reserve_inventory (
                sku VARCHAR(255) COLLATE utf8mb4_unicode_ci,
                off_site_inventory INT,
                PRIMARY KEY (sku)
            )');

            DB::table('temp_reserve_inventory')->insert($insert_data->toArray());

            // Update products table using the temporary table
            DB::statement('UPDATE products p
                INNER JOIN temp_reserve_inventory tri ON p.sku = tri.sku
                SET p.other_inventory = tri.off_site_inventory,
                    p.updated_at = now()
                WHERE p.deleted_at IS NULL');

            // Drop the temporary table
            DB::statement('DROP TEMPORARY TABLE IF EXISTS temp_reserve_inventory');
        });
    }

    public function tags()
    {
        return ['sync-subscription-reserve-inventory'];
    }
}
