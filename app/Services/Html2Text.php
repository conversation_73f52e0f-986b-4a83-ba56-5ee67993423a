<?php

namespace App\Services;

class Html2Text extends \Html2Text\Html2Text
{
    /**
     * Overriding method to not append link list
     */
    protected function doConvert(): void
    {
        $this->linkList = [];

        $text = trim($this->html);

        $this->converter($text);

        $this->text = $text;

        $this->converted = true;
    }

    /**
     * Adding in our merge tags to base converter functionality
     */
    protected function converter(&$text): void
    {
        parent::converter($text);

        // Convert HTML merge tags to text versions
        $mergeTags = [
            '{order_items}' => '{order_items_text}',
            '{order_payments}' => '{order_payments_text}',
            '{order_summary}' => '{order_summary_text}'
        ];

        $text = str_ireplace(array_keys($mergeTags), array_values($mergeTags), $text);
    }

    /**
     * Overriding the default "to uppercase" strong tag functionality
     *
     * @param  array  $matches PREG matches
     */
    protected function pregCallback($matches)
    {
        if (mb_strtolower($matches[1]) === 'strong') {
            return $matches[3];
        }

        return parent::pregCallback($matches);
    }
}
