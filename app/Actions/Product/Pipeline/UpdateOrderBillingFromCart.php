<?php

namespace App\Actions\Product\Pipeline;

use App\Models\Card;
use App\Models\Order;
use App\Models\Payment;
use Closure;

class UpdateOrderBillingFromCart
{
    public function handle(array $passable, Closure $next): Order
    {
        /**
         * @var Order $order
         * @var array $params
         */
        list($order, $params) = $passable;

        $billing = $params['billing'];

        $payment = Payment::where('key', $billing['method'])->select('id')->first();

        $order->payment_id = $payment->id;

        if ($billing['method'] === 'card') {
            $card = Card::where('source_id', $billing['source_id'])->first();
            $order->payment_source_id = $card?->id;

            if ($billing['save_for_later']) {
                $order->customer->checkout_card_id = $card?->id;
                Card::where('user_id', $order->customer_id)->update(['default' => false]);
                $card?->update(['default' => true]);

                if ($order->customer->isDirty()) {
                    $order->customer->save();
                }
            }
        }

        return $next([$order, $params]);
    }
}
