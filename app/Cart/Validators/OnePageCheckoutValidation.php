<?php

namespace App\Cart\Validators;

use App\Contracts\Cartable;
use Illuminate\Pipeline\Pipeline;

class OnePageCheckoutValidation
{
    /**
     * @throws CartValidationException
     */
    public function validate(Cartable $cart, bool $validate_delivery_zone = false): void
    {
        $validations = [
            IsNotEmpty::class,
            DeliveryMethodIsAcceptingNewOrders::class,
            DeadlineHasPassed::class,
            MeetsDeliveryMethodOrderMinimum::class,
            HasAvailableProductQuantities::class,
            MeetsProductOrderMinimums::class,
            HasValidSubscription::class,
        ];

        if ($validate_delivery_zone) {
            $validations[] = HasCompleteShippingAddress::class;
            $validations[] = IsWithinDeliveryMethodCoverage::class;
        }

        app(Pipeline::class)
            ->send($cart)
            ->through($validations)
            ->thenReturn();
    }
}
