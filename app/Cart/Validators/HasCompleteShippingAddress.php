<?php

namespace App\Cart\Validators;

use App\Contracts\Cartable;

class HasCompleteShippingAddress
{
    /**
     * @throws CartValidationException
     */
    public function handle(Cartable $cart, \Closure $next): Cartable
    {
        // Only validate shipping address for delivery zones
        if ($cart->cartLocation()?->isDeliveryZone() && !$cart->hasShippingInfo()) {
            throw new CartValidationException(
                rule: HasCompleteShippingAddress::class,
                message: 'Please provide a complete shipping address including street, city, state, and zip code.'
            );
        }

        return $next($cart);
    }
}
