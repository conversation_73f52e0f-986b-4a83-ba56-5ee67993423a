<?php

namespace App\Livewire\Theme;

use App\Contracts\Cartable;
use App\Models\Cart;
use App\Models\Pickup;
use App\Models\User;
use App\Services\StoreService;

trait FetchesCart
{
    public function fetchShopperCart(bool $should_stub = true): ?Cartable
    {
        $cart = app(StoreService::class)->cart();

        if (is_null($cart) && $should_stub) {
            $cart = $this->stubShopperCart();
        }

        return $cart;
    }

    private function stubShopperCart(): Cartable
    {
        return auth()->user()
            ? $this->stubUserCart(auth()->user())
            : $this->stubGuestCart();
    }

    private function stubUserCart(User $user): Cartable
    {
        return Cart::stub($user);
    }

    private function stubGuestCart(): Cartable
    {
        $cart = Cart::stub(new User);

        if (request()->hasCookie('shopping_delivery_method_id')) {
            $pickup = Pickup::find(request()->cookie('shopping_delivery_method_id'));

            if ( ! is_null($pickup)) {
                $cart->stubCartLocation($pickup);
            }
        }

        return $cart;
    }
}
