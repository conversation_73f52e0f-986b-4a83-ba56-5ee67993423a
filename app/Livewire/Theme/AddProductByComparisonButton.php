<?php

namespace App\Livewire\Theme;

use Illuminate\Contracts\View\View as ViewContract;
use Illuminate\Support\Collection;
use Livewire\Component;

class AddProductByComparisonButton extends Component
{
    use AddsProduct;

    public ?int $selected_variant_id = null;

    public Collection $comparable_products;

    public string $cart_label = '';

    public function mount()
    {
        $this->selected_variant_id = $this->comparable_products->first()?->id;
    }

    public function render(): ViewContract
    {
        return view('theme::livewire.add-product-by-comparison-button');
    }

    public function add()
    {
        match(true) {
            $this->has_subscription => $this->addToSubscription($this->selected_variant_id),
            $this->has_order => $this->addToOrder($this->selected_variant_id),
            default => $this->addToCart($this->selected_variant_id),
        };
    }
}
