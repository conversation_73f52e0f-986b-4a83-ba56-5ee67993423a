<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Theme\Modals\ModalAttributes;
use Livewire\Attributes\On;
use Livewire\Component;

class BulkEditSubscriptions extends Component
{
    use ModalAttributes;

    public string $action = 'date';

    public ?string $bulk_selection_type = null;

    public array $subscription_ids = [];

    public int $subscription_count = 0;

    public ?string $delivery_date = null;

    public string $product_action = 'remove';

    public ?int $removed_product_id = null;

    public ?int $replacement_product_id = null;

    public function render()
    {
        return view('livewire.modals.bulk-edit-subscriptions');
    }

    public function submit()
    {
        $this->dispatch(
            event: 'bulk-action-selected',
            action: $this->action,
            params: $this->bulkActionParams()
        );

        $this->close();
    }

    protected function bulkActionParams()
    {
        return $this->action === 'date'
            ? [
                'delivery_date' => $this->delivery_date,
                'bulk_selection_type' => $this->bulk_selection_type,
                'subscription_ids' => $this->subscription_ids,
            ]
            : [
                'product_action' => $this->product_action,
                'removed_product_id' => $this->removed_product_id,
                'replacement_product_id' => $this->replacement_product_id,
                'bulk_selection_type' => $this->bulk_selection_type,
                'subscription_ids' => $this->subscription_ids,
            ];
    }

    #[On('close-modal-bulk-edit-subscriptions')]
    public function close(): void
    {
        $this->reset();
        $this->closeModal();
    }

    #[On('open-modal-bulk-edit-subscriptions')]
    public function open(?string $bulk_selection_type, array $subscription_ids, int $subscription_count): void
    {
        $this->bulk_selection_type = $bulk_selection_type;
        $this->subscription_ids = $subscription_ids;
        $this->subscription_count = $subscription_count;
        $this->openModal();
    }

    protected function rules(): array
    {
        return $this->action === 'date'
            ? ['delivery_date' => ['required', 'date', 'after_or_equal:today']]
            : [
                'product_action' => ['required', 'in:remove,replace'],
                'removed_product_id' => ['required', 'exists:products,id'],
                'replacement_product_id' => ['nullable', 'required_if:product_action,replace', 'exists:products,id'],
            ];
    }
}
