<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\DeliverySalesExport;
use App\Http\Controllers\Controller;
use App\Models\Filter;
use App\Repositories\Reports\DeliverySalesReport;
use App\Services\FilterService\Filters\Filter as AppliedFilter;
use App\Support\Enums\OrderStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Excel;

class DeliverySalesReportController extends Controller
{
    public function index(Request $request)
    {
        $request->mergeIfMissing([
            'pickup_date.start' => today()->subMonth()->format('M jS Y'),
            'pickup_date.end' => today()->format('M jS Y'),
            'order_status' => OrderStatus::ids([OrderStatus::canceled()])->toArray(),
            'orderBy' => 'pickup_title',
            'sort' => 'asc',
        ]);

        $is_export = $request->has('export');

        $results = (new DeliverySalesReport)->handle($request->all(), $is_export);

        if ($is_export) {
            return (new DeliverySalesExport($results))
                ->download(
                    'delivery_sales_'.
                    $request->appliedFilters()
                        ->map(fn (AppliedFilter $filter) => Str::slug("{$filter->label()} {$filter->value()}"))
                        ->implode('_').'.csv',
                    Excel::CSV
                );
        }

        $filters = Filter::where('type', 'location_sales_report')->get();

        return view('reports.delivery-sales.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'results' => $results,
            ]);
    }
}
