<?php

namespace App\Http\Controllers\Theme\Store;

use App\Events\Product\ProductWasViewed;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Theme\Concerns\FetchesCustomPage;
use App\Models\Page;
use App\Repositories\StoreRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\View\View;

class StoreController extends Controller
{
    use FetchesCustomPage;

    public function index(Request $request): View
    {
        session(['store_url' => $request->fullUrl()]);

        $repository = new StoreRepository($request);

        $pageTitle = setting('store_page_title', 'Shop Now');
        $heading = null;
        $subheading = setting('store_page_description');
        $collection = null;
        $tags = collect();

        if (config('grazecart.new_storefront_enabled')) {
            $categories = (new StoreRepository($request))->categoriesAndProducts();
            return view('theme::store.new-index', compact('categories'));
        }

        $default_collection_id = setting('store_default_collection');

        // Check if the default store view uses a collection or page.
        if ($default_collection_id && ! $request->filled('q')) {
            $store_landing_page = $this->getStoreLandingPage($default_collection_id);

            if (! is_null($store_landing_page)) {
                [$page, $html] = $this->fetchCustomPage($store_landing_page->slug);
                return view('theme::store.landing')
                    ->with(compact('page', 'html'));
            }

            $collection = $repository->getCollection($default_collection_id, column: 'id');

            if (is_null($collection)) {
                $query = $repository->getDefaultProducts($request);
            } else {
                $tags = $repository->tags($collection->products->pluck('id')->toArray(), $collection->slug);
                $tag = $tags->where('slug', $request->get('tag'))->first();
                $pageTitle = $tag ? $collection->getMetaTitle() . ': ' . $tag->title : $collection->getMetaTitle();
                $subheading = $collection['description'];
                $heading = $collection->getPageHeading();
                $query = $repository->getProductsInCollection($request, $collection);
            }
        } else {
            $query = $repository->getDefaultProducts($request);
        }

        $products = $query->simplePaginate(setting('store_products_per_page', 50));

        return view('theme::store.index')
            ->with([
                'pageTitle' => $pageTitle,
                'heading' => $heading,
                'subheading' => $subheading,
                'pageDescription' => $subheading,
                'headerPhoto' => $collection?->cover_photo,
                'tags' => $tags,
                'pageCanonical' => $collection?->getCanonicalUrl(),
                'products' => $products,
            ]);
    }

    private function getStoreLandingPage(string $collection_name): ?Page
    {
        $prefix = substr($collection_name, 0, 4);

        if ($prefix !== 'page') {
            return null;
        }

        return Page::query()
            ->select(['slug'])
            ->find(str_replace('page_', '', $collection_name));
    }

    public function show(Request $request, string $slug)
    {
        try {
            $product = (new StoreRepository($request))
                ->getIndividualProduct($slug)
                ->firstOrFail();

        } catch (ModelNotFoundException $e) {
            error('The product you are looking for could not be found.');
            return back(301);
        }

        if ($user = auth()->user()) {
            ProductWasViewed::dispatch($user, $product);
        }

        return view('theme::store.show')
            ->with(compact('product'));
    }
}
