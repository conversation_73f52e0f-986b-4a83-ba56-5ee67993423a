<?php

namespace App\Http\Controllers\Theme\Order;

use App\Contracts\CartService;
use App\Http\Controllers\Controller;

class CartShortStockController extends Controller
{
    public function __invoke(CartService $cart_service)
    {
        $shopper = request()->shopper();

        $cart = $cart_service->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);

        if (is_null($cart) || $cart->cartIsEmpty()) {
            flash('The cart is empty.');
            return redirect()->route('cart.show');
        }

        try {
            $cart->removeOutOfStockItems(true);
        } catch (\Exception $e) {
            flash('Unable to update cart. ' . $e->getMessage());
            return redirect()->route('cart.show');
        }

        /** @phpstan-ignore-next-line  */
        if ($cart->cartIsEmpty()) {
            flash('All items were removed from the cart.');
            return redirect()->route('cart.show');
        }

        flash('The cart has been updated and the total was adjusted.');
        return back();
    }
}
