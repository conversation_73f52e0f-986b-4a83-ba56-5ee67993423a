<?php

namespace App\Http\Controllers\API\Theme;

use App\Models\GiftCertificate;
use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class RedeemedGiftCertificateController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'code' => [
                'required',
                Rule::exists(GiftCertificate::class, 'code')->where('active', true)
            ]
        ]);

        $certificate = GiftCertificate::where('code', $validated['code'])
            ->with('redeemer')
            ->first();

        if ($certificate->isRedeemed()) {
            return $this->makeResponse($request, 'The gift certificate has already been redeemed.', 409);
        }

        auth()->user()->redeemGiftCertificate($certificate);

        if ($request->filled('order') && $request->expectsJson()) {
            $order = Order::with(['discounts', 'customer'])
                ->where('id', $request->get('order'))
                ->where('customer_id', auth()->user()->id)
                ->firstOrFail();

            $order->updateTotals();

            return response()->json([
                'certificate_amount' => money($certificate->amount),
                'summary' => view('theme::order.partials.summary')->with(compact('order'))->render(),
                'total' => money($order->total)
            ]);
        }

        return $this->makeResponse(
            $request,
            'The gift certificate in the amount of &#36;' . money($certificate->amount) . ' has been applied.'
        );
    }

    private function makeResponse(Request $request, string $message, int $code = 200)
    {
        if ($request->expectsJson()) {
            return response()->json($message, $code);
        }

        if ($code !== 200) {
            error($message);
            return back();
        }

        flash($message);
        return back();
    }
}
