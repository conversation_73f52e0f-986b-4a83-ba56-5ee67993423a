<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Detour;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DetourController extends Controller
{
    public function index()
    {
        return Detour::query()
            ->select(['id', 'from_url', 'to_url', 'status_code'])
            ->orderBy('from_url')
            ->get();
    }

    public function store(Request $request): JsonResponse
    {
        $request['from_url'] = ltrim(str_replace(' ', '/', $request['from_url']), '/\\');
        $request['to_url'] = ltrim(str_replace(' ', '/', $request['to_url']), '/\\');

        $request->validate([
            'from_url' => ['required', 'unique:detours,from_url'],
            'to_url' => ['required', 'unique:detours,from_url', 'different:from_url'],
            'status_code' => ['required', 'integer', 'min:301', 'max:302']
        ], [
            'from_url.required' => 'The origin url field is required.',
            'from_url.unique' => 'The origin url field has already been taken.',
            'to_url.required' => 'The destination url field is required.',
            'to_url.unique' => 'The destination url field cannot be the same as an existing origin url.',
            'to_url.different' => 'The origin url field must be different from the destination url.',
        ]);

        $detour = Detour::create($request->all());

        return response()->json($detour, 201);
    }

    public function update(Request $request, Detour $detour): JsonResponse
    {
        $request['from_url'] = ltrim(str_replace(' ', '/', $request['from_url']), '/\\');
        $request['to_url'] = ltrim(str_replace(' ', '/', $request['to_url']), '/\\');

        $validated = $request->validate([
            'from_url' => ['required', 'unique:detours,from_url,' . $detour->id],
            'to_url' => ['required', 'different:from_url', 'unique:detours,from_url,' . $detour->id],
            'status_code' => ['required', 'integer', 'min:301', 'max:302']
        ], [
            'from_url.required' => 'The origin url field is required.',
            'from_url.unique' => 'The origin url field has already been taken.',
            'to_url.required' => 'The destination url field is required.',
            'to_url.unique' => 'The destination url field cannot be the same as an existing origin url.',
            'to_url.different' => 'The origin url field must be different from the destination url.',
        ]);

        $detour->update($validated);

        return response()->json($detour);
    }

    public function destroy(Detour $detour): Response
    {
        $detour->delete();

        return response()->noContent();
    }
}
