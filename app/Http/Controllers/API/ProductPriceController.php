<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Models\ProductPrice;
use Illuminate\Http\Request;

class ProductPriceController extends Controller
{
    public function index(int $productId)
    {
        return ProductPrice::with(['group'])->where('product_id', $productId)->get();
    }

    public function store(Request $request, int $productId): JsonResponse
    {
        $request->validate([
            'group_id' => ['required'],
            'unit_price' => ['required', 'numeric', 'min:0'],
            'sale_unit_price' => ['required', 'numeric', 'min:0']
        ]);

        if (ProductPrice::where('group_id', $request->get('group_id'))->where('product_id', $productId)->first()) {
            return response()->json('This price group has already been set up for this product.', 422);
        }

        $product = ProductPrice::create([
            'group_id' => $request->get('group_id'),
            'product_id' => $productId,
            'unit_price' => formatCurrencyForDB($request->get('unit_price')),
            'sale_unit_price' => formatCurrencyForDB($request->get('sale_unit_price')),
        ]);

        return response()->json($product);
    }

    public function update(Request $request, int $productId, int $productPriceId): JsonResponse
    {
        $request->validate([
            'unit_price' => ['required', 'numeric', 'min:0'],
            'sale_unit_price' => ['required', 'numeric', 'min:0']
        ]);

        $product = ProductPrice::where('product_id', $productId)
            ->where('id', $productPriceId)
            ->update([
                'unit_price' => formatCurrencyForDB($request->get('unit_price')),
                'sale_unit_price' => formatCurrencyForDB($request->get('sale_unit_price')),
                'unit_of_issue' => $request->get('unit_of_issue'),
                'weight' => $request->get('weight'),
            ]);

        return response()->json($product);
    }

    public function destroy(int $productId, int $productPriceId): JsonResponse
    {
        ProductPrice::where('product_id', $productId)
            ->where('id', $productPriceId)
            ->delete();

        return response()->json('Price deleted');
    }
}
