<?php

namespace App\Http\Controllers\API;

use App\Cart\Cart;
use App\Cart\Validators\CartValidationException;
use App\Cart\Validators\DeadlineHasPassed;
use App\Cart\Validators\DeliveryMethodIsAcceptingNewOrders;
use App\Cart\Validators\HasAvailableProductQuantities;
use App\Cart\Validators\HasCompleteShippingAddress;
use App\Cart\Validators\HasValidSubscription;
use App\Cart\Validators\IsWithinDeliveryMethodCoverage;
use App\Cart\Validators\MeetsDeliveryMethodOrderMinimum;
use App\Cart\Validators\MeetsProductOrderMinimums;
use App\Cart\Validators\OnePageCheckoutValidation;
use App\Contracts\Cartable;
use App\Events\Cart\CartUpdated;
use App\Events\Order\OrderWasConfirmed;
use App\Events\Subscription\RecurringOrderCreated;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\CartConfirmationRequest;
use App\Models\Date;

class CartConfirmationController extends Controller
{
    public function __invoke(CartConfirmationRequest $request)
    {
        /** @var Cartable|null $cartable */
        $cartable = $this->shopperCart();

        if (is_null($cartable)) {
            return response()->json(['message' => 'Shopping cart does not exist.'], 409);
        }

        // This ensures date selected at checkout is validated vs what is already set on cartable
        if ( ! is_null($request->get('date_id'))) {
            $date = Date::find($request->get('date_id'));

            if ( ! is_null($date)) {
                $cartable->updateCartDate($date);
                event(new CartUpdated($cartable));
            }
        }

        if ($cartable->cartLocation()?->isDeliveryZone()) {
            $current_attributes = $cartable->getShippingInfo();

            // This ensures address entered at checkout is validated vs what is set on cartable. Can be removed when refactoring to just use Cartable
            $cartable->setShippingInfo([
                'street' => $request['shipping']['street'] ?? $current_attributes['street'],
                'street_2' => $request['shipping']['street_2'] ?? $current_attributes['street_2'],
                'city' => $request['shipping']['city'] ?? $current_attributes['city'],
                'state' => $request['shipping']['state'] ?? $current_attributes['state'],
                'zip' => $request['shipping']['zip'] ?? $current_attributes['zip'],
                'country' => $request['shipping']['country'] ?? $current_attributes['country'],
            ]);

            event(new CartUpdated($cartable));

            if($request['shipping']['save_for_later'] ?? false) {
                $cartable->cartCustomer()->updateAddress($request['shipping']);
            }
        }

        $cart_from_request = Cart::fromRequest($request);

        try {
            app(OnePageCheckoutValidation::class)->validate(cart: $cartable, validate_delivery_zone: true);
        } catch (CartValidationException $exception) {
            return $this->handleFailedCartValidation($exception);
        }

        $order = $cartable->confirmWithCart($cart_from_request);

        event(new OrderWasConfirmed($order));

        if ($order->isFromBlueprint()) {
            event(new RecurringOrderCreated($order->blueprint));
        }

        session(['last_confirmed_order' => $order->id, 'cart' => null]);

        session()->flash('orderWasConfirmed');

        return response()->json(compact('order'), 201);
    }

    protected function handleFailedCartValidation(CartValidationException $exception)
    {
        $message = match($exception->rule) {
            DeadlineHasPassed::class => 'The selected delivery date is no longer available.',
            HasValidSubscription::class => 'Subscription information is required.',
            MeetsProductOrderMinimums::class, MeetsDeliveryMethodOrderMinimum::class,
            DeliveryMethodIsAcceptingNewOrders::class, IsWithinDeliveryMethodCoverage::class,
            HasCompleteShippingAddress::class => $exception->getMessage(),
            HasAvailableProductQuantities::class =>
                'Requested products are no longer available: ' . $exception->data['items']->implode(fn (array $item) => $item['item']->product?->title, ', '),
            default => 'There was an error processing your order. Please try again.'
        };

        return response()->json(compact('message'), 409);
    }
}
