<?php

namespace App\Http\Controllers\API\Pages;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Models\Widget;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PageWidgetController extends Controller
{
    public function index(Page $page)
    {
        return $page->widgets()
            ->orderBy('sort')
            ->get();
    }

    public function store(Request $request, Page $page): JsonResponse
    {
        $request->validate([
            'id' => ['required'],
            'sort' => ['required'],
        ]);

        $blueprint = (new Widget)->fill([
            'enabled' => true,
            'template' => $request->input('id'),
            'sort' => $request->input('sort')
        ])->getConfig();

        $widget = (new Widget)->fill([
            'enabled' => true,
            'title' => $blueprint['title'],
            'template' => $request->input('id'),
            'settings' => $blueprint['settings'] ?? null,
            'sort' => $request->input('sort')
        ]);

        $page = $page->insertWidget($widget);

        $page->touch();

        $page->widgets()->save($widget);

        return response()->json([
            'page' => $page,
            'block' => $widget,
            'id' => $widget->id
        ]);
    }

    public function show(Page $page, Widget $widget)
    {
        $defaults = $widget->getConfig()['settings'];
        $widget['settings'] = array_merge($defaults, (array) $widget->settings);
        return $widget;
    }

    public function update(Request $request, Page $page, Widget $widget): JsonResponse
    {
        $validated = $request->validate([
            'title' => ['required'],
            'content' => ['nullable', 'string'],
            'settings' => ['array']
        ]);

        $widget = $widget->update($validated);

        $page->touch();

        return response()->json([
            'block' => $widget,
            'page_id' => $page->id
        ]);
    }

    public function destroy(Page $page, Widget $widget): JsonResponse
    {
        $widget->delete();

        return response()->json([
            'page_id' => $page->id
        ]);
    }
}
