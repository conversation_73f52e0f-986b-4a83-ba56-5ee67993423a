<?php

namespace App\Http\Middleware;

use App\Models\Pickup;
use Illuminate\Http\Request;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    protected $rootView = 'pos.app';

    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    public function share(Request $request): array
    {
        return array_merge(parent::share($request), [
            'flash_notification' => function () use ($request) {
                return $request->session()->get('flash_notification');
            },
            'auth.user' => function () use ($request) {
               return $request->user()
                    ? $request->user()->only(['id', 'first_name', 'email'])
                    : null;
            },

            'reader' => function ()  {
                return session('pos:reader');
            },
            'stripe_terminal_location_id' => function ()  {
                return setting('pos_location_id') ? Pickup::find(setting('pos_location_id'))?->stripe_terminal_location_id : null;
            }
        ]);
    }
}
