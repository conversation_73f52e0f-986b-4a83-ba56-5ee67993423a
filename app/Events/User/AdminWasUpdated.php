<?php

namespace App\Events\User;

use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminWasUpdated
{
    use Dispatchable, SerializesModels;

    public $user;
    public $originalEmail;

    public function __construct(User $user, string $originalEmail = null)
    {
        $this->user = $user;
        $this->originalEmail = $originalEmail;
    }
}
