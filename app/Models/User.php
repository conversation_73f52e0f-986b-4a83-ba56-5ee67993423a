<?php

namespace App\Models;

use App\Actions\Billing\AddPaymentMethodToCustomer;
use App\Actions\Billing\AddSourceToCustomer;
use App\Actions\Cart\CreateCartFromOrder;
use App\Billing\Gateway\PaymentMethod;
use App\Contracts\Billing;
use App\Contracts\CartService;
use App\Exceptions\OrderChargeException;
use App\Exceptions\OrderDoesNotMeetRequirementsException;
use App\Exceptions\PickupNotFoundException;
use App\Presenters\UserPresenter;
use App\Queries\CurrentOrderQuery;
use App\Services\PhoneNumberService;
use App\Services\SettingsService;
use App\Support\Enums\UserRole;
use App\Tasks\CreatePreOrder\CreatePreOrder;
use App\Traits\SettingsTrait;
use Database\Factories\UserFactory;
use Eloquent;
use EloquentFilter\Filterable;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphPivot;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection as IlluminateCollection;
use Illuminate\Support\Once;
use Illuminate\Support\Str;
use Laracasts\Presenter\PresentableTrait;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;

/**
 * App\Models\User
 *
 * @property int $id
 * @property string $email
 * @property string|null $email_alt
 * @property string|null $password
 * @property int $role_id
 * @property int|null $pricing_group_id
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $full_name
 * @property string|null $company_name
 * @property string|null $profile_photo
 * @property string|null $accounting_id
 * @property string|null $phone
 * @property string|null $cell_phone
 * @property string|null $street
 * @property string|null $street_2
 * @property string|null $city
 * @property string|null $state
 * @property string|null $zip
 * @property string|null $country
 * @property string $billing_street
 * @property string $billing_street_2
 * @property string $billing_city
 * @property string $billing_state
 * @property string|null $billing_zip
 * @property string $billing_country
 * @property float|null $lat
 * @property float|null $lng
 * @property int $order_id
 * @property int $credit
 * @property string | null $customer_id
 * @property int $checkout_card_id
 * @property int $billing_same_as_shipping
 * @property bool $newsletter
 * @property int $order_deadline_email_reminder
 * @property int $order_deadline_text_reminder
 * @property int $needs_reminded
 * @property Carbon|null $subscribed_to_sms_marketing_at
 * @property bool $active
 * @property int $order_count
 * @property int $order_skip_count
 * @property int $recurring_order_count
 * @property Carbon|null $last_purchase
 * @property Carbon|null $last_login
 * @property int $pickup_point
 * @property int $delivery_zone
 * @property int $referral_source_id
 * @property string|null $referral_description
 * @property int $referral_user_id
 * @property string|null $referral_code
 * @property string|null $referral_url
 * @property int|null $referral_bonus
 * @property int $referral_bonus_earned
 * @property int|null $referral_payout
 * @property int $can_backorder
 * @property int $is_leader
 * @property int $exempt_from_fees
 * @property int $agrees_to_terms
 * @property mixed $settings
 * @property string|null $notes
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|Card[] $cards
 * @property-read int|null $cards_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Event[] $credits
 * @property-read int|null $credits_count
 * @property-read Card|null $defaultCard
 * @property mixed $custom_fields
 * @property-read string $full_name_with_email
 * @property-read mixed $roll
 * @property-read DatabaseNotificationCollection|DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Order[] $orders
 * @property-read int|null $orders_count
 * @property-read Pickup|null $pickup
 * @property-read RecurringOrder|null $recurringOrder
 * @property-read \Illuminate\Database\Eloquent\Collection|RecurringOrderItem[] $recurringOrderItems
 * @property-read int|null $recurring_order_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|User[] $referrals
 * @property-read int|null $referrals_count
 * @property-read User|null $referredBy
 * @property-read \Illuminate\Database\Eloquent\Collection|Tag[] $tags
 * @property-read int|null $tags_count
 * @method static Builder|User admins()
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static Builder|User query()
 * @method static Builder|User staff()
 * @method static Builder|User whereAccountingId($value)
 * @method static Builder|User whereActive($value)
 * @method static Builder|User whereAgreesToTerms($value)
 * @method static Builder|User whereBillingCity($value)
 * @method static Builder|User whereBillingCountry($value)
 * @method static Builder|User whereBillingSameAsShipping($value)
 * @method static Builder|User whereBillingState($value)
 * @method static Builder|User whereBillingStreet($value)
 * @method static Builder|User whereBillingStreet2($value)
 * @method static Builder|User whereBillingZip($value)
 * @method static Builder|User whereCanBackorder($value)
 * @method static Builder|User whereCellPhone($value)
 * @method static Builder|User whereCheckoutCardId($value)
 * @method static Builder|User whereCity($value)
 * @method static Builder|User whereCompanyName($value)
 * @method static Builder|User whereCountry($value)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereCredit($value)
 * @method static Builder|User whereCustomerId($value)
 * @method static Builder|User whereDeliveryZone($value)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailAlt($value)
 * @method static Builder|User whereExemptFromFees($value)
 * @method static Builder|User whereFirstName($value)
 * @method static Builder|User whereFullName($value)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereIsLeader($value)
 * @method static Builder|User whereLastLogin($value)
 * @method static Builder|User whereLastName($value)
 * @method static Builder|User whereLastPurchase($value)
 * @method static Builder|User whereLat($value)
 * @method static Builder|User whereLng($value)
 * @method static Builder|User whereNeedsReminded($value)
 * @method static Builder|User whereNewsletter($value)
 * @method static Builder|User whereNotes($value)
 * @method static Builder|User whereOrderCount($value)
 * @method static Builder|User whereOrderDeadlineEmailReminder($value)
 * @method static Builder|User whereOrderDeadlineTextReminder($value)
 * @method static Builder|User whereOrderId($value)
 * @method static Builder|User whereOrderSkipCount($value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User wherePhone($value)
 * @method static Builder|User wherePickupPoint($value)
 * @method static Builder|User wherePricingGroupId($value)
 * @method static Builder|User whereProfilePhoto($value)
 * @method static Builder|User whereRecurringOrderCount($value)
 * @method static Builder|User whereReferralBonus($value)
 * @method static Builder|User whereReferralBonusEarned($value)
 * @method static Builder|User whereReferralCode($value)
 * @method static Builder|User whereReferralDescription($value)
 * @method static Builder|User whereReferralPayout($value)
 * @method static Builder|User whereReferralSourceId($value)
 * @method static Builder|User whereReferralUrl($value)
 * @method static Builder|User whereReferralUserId($value)
 * @method static Builder|User whereRememberToken($value)
 * @method static Builder|User whereRoleId($value)
 * @method static Builder|User whereSettings($value)
 * @method static Builder|User whereState($value)
 * @method static Builder|User whereStreet($value)
 * @method static Builder|User whereStreet2($value)
 * @method static Builder|User whereSubscribedToSmsMarketingAt($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereZip($value)
 * @property string $name
 * @property string $biography
 * @property string $position
 * @property-read bool $exempt_from_tax
 * @property-read bool $is_subscriber
 * @property-read string $role
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static Builder|User customers()
 * @method static UserFactory factory($count = null, $state = [])
 * @method static Builder|User filter(array $input = [], $filter = null)
 * @method static Builder|User hasExpiredGenericTrial()
 * @method static Builder|User onGenericTrial()
 * @method static Builder|User owners()
 * @method static Builder|User paginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static Builder|User receivesLastChanceEmail()
 * @method static Builder|User simplePaginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static Builder|User whereBeginsWith($column, $value, $boolean = 'and')
 * @method static Builder|User whereBiography($value)
 * @method static Builder|User whereEndsWith($column, $value, $boolean = 'and')
 * @method static Builder|User whereLike($column, $value, $boolean = 'and')
 * @method static Builder|User whereName($value)
 * @method static Builder|User wherePosition($value)
 * @mixin Eloquent
 */
class User extends Authenticatable
{
    use HasFactory;

    use HasApiTokens, PresentableTrait, Authorizable, SettingsTrait, Filterable, Notifiable;

    protected $table = 'users';

    protected $guarded = ['id', 'credit', 'order_count', 'role_id'];

    protected $hidden = ['password', 'remember_token'];

    protected $appends = ['full_name', 'role', 'full_name_with_email'];

    protected string $presenter = UserPresenter::class;

    public static function notificationRecipients(string $notification): array
    {
        return self::select(['first_name', 'last_name', 'email', 'settings'])
            ->admins()
            ->get()
            ->filter(function (User $user) use ($notification) {
                return isset($user->settings->{$notification})
                    && $user->settings->{$notification} == 1
                    && $user->email !== setting('email_general');
            })
            ->flatMap(fn(User $user) => [$user->email => "{$user->first_name} {$user->last_name}"])
            ->toArray();
    }

    public static function register(IlluminateCollection $attributes, User $existingUsing = null): User
    {
        $user = $existingUsing ?? new static;
        $user->first_name = htmlspecialchars(trim($attributes->get('first_name')));
        $user->last_name = htmlspecialchars(trim($attributes->get('last_name')));
        $user->email = trim($attributes->get('email'));
        $user->password = $attributes->get('password');
        $user->accounting_id = $user->createAccountingId();
        $user->pickup_point = $attributes->get('pickup_point', 0);
        $user->city = $attributes->get('city', '');
        $user->state = $attributes->get('state', '');
        $user->zip = $attributes->get('postal_code', '');
        $user->country = $attributes->get('country', '');
        $user->full_name = $user->first_name . ' ' . $user->last_name;
        $user->role_id = $attributes->get('type') ? $attributes->get('type') : UserRole::customer();
        $user->referral_code = $user->createReferralCode();
        $user->applyRegistrationCredit();
        $user->newsletter = $attributes->has('subscribe_to_newsletter');
        if ( ! empty($attributes->get('referral_code'))) {
            $user->applyReferralCredit($attributes->get('referral_code'));
        }
        $user->active = ! setting('require_user_activation');
        $user->save();
        return $user;
    }

    /**
     * Create accounting ID;
     */
    public function createAccountingId(int $count = 1, array $options = []): ?string
    {
        $firstName = $options['first_name'] ?? $this->first_name;
        $lastName = $options['last_name'] ?? $this->last_name;
        $email = $options['email'] ?? $this->email;

        if (empty($firstName) || empty($lastName)) {
            return null;
        }

        if ($count === 1) {
            $accountingId = $firstName . ' ' . $lastName . ' (' . $email . ')';
        } else {
            $accountingId = $firstName . ' ' . $lastName . ' (' . $email . ')_' . $count;
        }

        if ($this->where('accounting_id', $accountingId)->first()) {
            return $this->createAccountingId($count + 1);
        } else {
            return $accountingId;
        }
    }

    public function createReferralCode(): string
    {
        return Str::random(12);
    }

    public function applyRegistrationCredit(): User
    {
        $this->applyCredit(
            formatCurrencyForDB(app(SettingsService::class)->registrationCredit()),
            'Registration bonus.'
        );
        return $this;
    }

    public function applyCredit(int $credit, string $reason = null): bool
    {
        if ($credit <= 0) {
            return false;
        }

        $this->credit += $credit;
        $this->save();

        $this->recordEvent('credit_applied', [
            'amount' => $credit,
        ], $reason);

        return true;
    }

    public function recordEvent(string $event_id, array $metadata, ?string $description = null): void
    {
        Event::create([
            'model_type' => User::class,
            'model_id' => $this->id,
            'description' => $description,
            'event_id' => $event_id,
            'user_id' => auth()->user()->id ?? $this->id,
            'metadata' => json_encode($metadata)
        ]);
    }

    public function applyReferralCredit(string $code): User
    {
        $referrer = $this->select(['id', 'referral_payout', 'first_name', 'last_name'])
            ->where('referral_code', $code)
            ->first();

        if ($referrer && setting('enable_referrals')) {
            // Assign the ID of the customer that referred them and apply the referral incentive.
            $this->referral_user_id = $referrer->id;
            $this->applyCredit(setting('referral_payout', 0) + $referrer->referral_payout, 'For being referred by ' . $referrer->full_name);
        }

        return $this;
    }

    public static function cron(): User
    {
        return User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'role_id' => 6,
            'accounting_id' => null,
            'referral_code' => null,
            'first_name' => 'Cron',
            'last_name' => 'User',
        ]);
    }

    public static function deletedCustomer(): User
    {
        return once(function () {
            return User::firstOrCreate([
                'email' => config('app.deleted_user_email'),
            ], [
                'role_id' => 4,
                'first_name' => 'Deleted',
                'last_name' => 'Customer',
                'password' => Str::random(40),
            ]);
        });
    }

    /**
     * @param  Builder<User>  $query
     * @return Builder<User>
     */
    public function scopeOwners(Builder $query): Builder
    {
        return $query->where('role_id', 1);
    }

    /**
     * @param  Builder<User>  $query
     * @return Builder<User>
     */
    public function scopeAdmins(Builder $query): Builder
    {
        return $query->whereIn('role_id', [1, 2]);
    }

    /**
     * @param  Builder<User>  $query
     * @return Builder<User>
     */
    public function scopeStaff(Builder $query): Builder
    {
        return $query->whereIn('role_id', UserRole::staff());
    }

    /**
     * @param  Builder<User>  $query
     * @return Builder<User>
     */
    public function scopeCustomers(Builder $query): Builder
    {
        return $query->where('role_id', 4);
    }

    /**
     * @param  Builder<User>  $query
     * @return Builder<User>
     */
    public function scopeReceivesLastChanceEmail(Builder $query): Builder
    {
        return $query->whereDoesntHave('recurringOrder')
            ->where('order_deadline_email_reminder', true)
            ->where('email', '!=', '');
    }

    /**
     * @throws OrderChargeException
     */
    public function addCard(string $token, array $params = []): Card
    {
        return app(AddSourceToCustomer::class)->handle($this, $token, $params);
    }

    public function addPaymentMethod(PaymentMethod $payment_method, bool $make_default = false): Card
    {
        return app(AddPaymentMethodToCustomer::class)->handle($this, $payment_method, $make_default);
    }

    public function removeCredit(int $credit, string $reason = null): bool
    {
        if ($credit <= 0) {
            return false;
        }

        $this->credit = max($this->credit - $credit, 0);
        $this->save();

        $this->recordEvent('credit_removed', [
            'amount' => $credit,
        ], $reason);

        return true;
    }

    /**
     * @throws OrderDoesNotMeetRequirementsException
     */
    public function reorderFromOrder(Order $order): CreateCartFromOrder
    {
        $service = app(CartService::class);

        $cart = $service->find(User::class, (string) $order->customer_id);

        if (is_null($cart)) {
            $cart = $service->create(User::class, (string) $order->customer_id);
        }

        if (is_null($cart->cartLocation()) || ! $cart->cartLocation()->isCurrentlyAcceptingOrders()) {
            throw new OrderDoesNotMeetRequirementsException('You can not reorder because the order window is currently closed.');
        }

        return app(CreateCartFromOrder::class)->handle($order, $cart);
    }

    public function updateFulfillmentLocation(Pickup $pickup): User
    {
        $this->pickup_point = $pickup->id;
        $this->save();

        return $this;
    }

    /**
     * @throws Exception
     */
    public function openOrder(): ?Order
    {
        $this->load('pickup');

        if ($this->pickup?->isInactive() ?? true) {
            throw new PickupNotFoundException;
        }

        $order = $this->queryOpenOrder();

        if (is_null($order)) {
            return null;
        }

        // If order has no pickup then ask user to choose a new default pickup location.
        if ($order->pickup?->isInactive() ?? true) {
            throw new PickupNotFoundException;
        }

        return $order;
    }

    public function queryOpenOrder(): ?Order
    {
        return once(fn() =>
            $this->currentOrder([
                'id', 'pickup_id', 'blueprint_id', 'schedule_id', 'total', 'original_subtotal',
                'original_weight', 'original_fees_subtotal', 'original_total', 'subtotal', 'tax',
                'order_discount', 'subscription_savings', 'coupon_subtotal', 'fees_subtotal',
                'credit_applied', 'delivery_rate', 'delivery_fee_type', 'delivery_fee',
                'deadline_date', 'confirmed', 'status_id', 'pickup_date', 'customer_id', 'is_recurring',
                'date_id', 'canceled', 'paid', 'shipping_city', 'shipping_state', 'shipping_zip',
            ])?->load(Order::relationsToLoad())
        );
    }

    public function currentOrder(array $select = ["*"]): ?Order
    {
        return once(fn() => app(CurrentOrderQuery::class)->run($this, $select));
    }

    /** @throws PickupNotFoundException */
    public function createOrder(array $params = []): Order
    {
        Once::flush();
        return Order::createForUser($this, $params);
    }

    /**
     * @param  Product  $product
     * @param  IlluminateCollection<string, mixed>  $params
     * @return Order
     * @throws Exception
     */
    public function createPreOrder(Product $product, IlluminateCollection $params): Order
    {
        return app(CreatePreOrder::class)->handle($this, $product, $params);
    }

    public function redeemGiftCertificate(GiftCertificate $certificate): User
    {
        $certificate->redeemer_id = $this->id;
        $certificate->redeemed = now();

        if ($certificate->qty > 0) {
            $certificate->qty = $certificate->qty - 1;
            $certificate->balance = 0;
        }

        $certificate->save();

        $this->applyCredit($certificate->amount, 'Redeemed gift card ' . $certificate->code);

        return $this;
    }

    public function getReferralUrl(): string
    {
        return $this->createReferralUrl();
    }

    public function createReferralUrl(): string
    {
        if (empty($this->referral_code)) {
            $this->referral_code = $this->createReferralCode();
            $this->save();
        }
        return url('/register?' . http_build_query([
            'referral_code' => $this->referral_code,
        ]));
    }

    public function creditReferralBonus(): User
    {
        $this->applyCredit(setting('referral_bonus', 0) + $this->referral_bonus, 'Referral bonus.');
        $this->referral_bonus_earned = $this->referral_bonus_earned + setting('referral_bonus', 0) + $this->referral_bonus;

        return $this;
    }

    public function needsPassword(): bool
    {
        return !strlen($this->password);
    }

    public function hasCustomerId(): bool
    {
        return ! empty($this->customer_id);
    }

    /**
     * Get the customer's default card.
     */
    public function getDefaultCard(): ?Card
    {
        return $this->cards()->firstWhere('default', true);
    }

    /**
     * @return HasMany<Card, $this>
     * @throws BindingResolutionException
     */
    public function cards(): HasMany
    {
        return $this->hasMany(Card::class, 'user_id', 'id')
            ->orderBy('created_at', 'desc');
    }

    public function hasCard(): bool
    {
        return $this->cards()->exists();
    }

    public function hasSecondaryEmail(): bool
    {
        return ! empty($this->secondaryEmail());
    }

    public function secondaryEmail(): ?string
    {
        return $this->email_alt;
    }

    public function isWholesaleCustomer(): bool
    {
        return $this->setting('pricing_tier') === 'wholesale';
    }

    public function isRetailCustomer(): bool
    {
        return $this->setting('pricing_tier', 'retail') === 'retail';
    }

    public function getCredit(): int
    {
        return (int) $this->credit;
    }

    public function hasCredit(): bool
    {
        return $this->credit > 0;
    }

    public function hasAdminAccess(): bool
    {
        return $this->isOwner() || $this->isStaff();
    }

    public function isOwner(): bool
    {
        return $this->role_id === UserRole::owner();
    }

    public function isStaff(): bool
    {
        return in_array($this->role_id, UserRole::staff()->toArray());
    }

    public function isEditor(): bool
    {
        return $this->role_id === UserRole::editor();
    }

    public function isCustomer(): bool
    {
        return $this->role_id === UserRole::customer();
    }

    public function isSupport(): bool
    {
        return $this->role_id === UserRole::support();
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function getExemptFromFeesAttribute(): bool
    {
        return $this->exemptFromFees();
    }

    public function exemptFromFees(): bool
    {
        return (bool) $this->getRawOriginal('exempt_from_fees');
    }

    public function getExemptFromTaxAttribute(): bool
    {
        return $this->exemptFromTax();
    }

    public function exemptFromTax(): bool
    {
        return (bool) $this->setting('exempt_from_tax', false);
    }

    public function agreesToTerms(): bool
    {
        return (bool) $this->agrees_to_terms;
    }

    public function canBackorder(): bool
    {
        return boolval($this->can_backorder);
    }

    public function hasRecurringOrder(): bool
    {
        return once(fn(): bool => $this->recurringOrder()->exists());
    }

    /**
     * @return HasOne<RecurringOrder, $this>
     */
    public function recurringOrder(): HasOne
    {
        return $this->hasOne(RecurringOrder::class, 'customer_id');
    }

    public function getSkipCount(): int
    {
        return $this->order_skip_count;
    }

    /**
     * @return BelongsToMany<Tag, $this>
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)->withTimestamps();
    }

    /**
     * @return HasOne<Pickup, $this>
     */
    public function pickup(): HasOne
    {
        return $this->hasOne(Pickup::class, 'id', 'pickup_point');
    }

    /**
     * @return HasMany<RecurringOrderItem, $this>
     */
    public function recurringOrderItems(): HasMany
    {
        return $this->hasMany(RecurringOrderItem::class, 'customer_id');
    }

    /**
     * @return HasMany<User, $this>
     */
    public function referrals(): HasMany
    {
        return $this->hasMany(User::class, 'referral_user_id', 'id')
            ->orderBy('created_at', 'desc');
    }

    /**
     * @return HasOne<User, $this>
     */
    public function referredBy(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'referral_user_id');
    }

    /**
     * @return HasOne<Card, $this>
     * @throws BindingResolutionException
     */
    public function defaultCard(): HasOne
    {
        return $this->hasOne(Card::class, 'user_id', 'id')
            ->where('default', true);
    }

    /**
     * @return IlluminateCollection<PaymentMethod>
     */
    public function stripePaymentMethods(): IlluminateCollection
    {
        return app(Billing::class)->retrieveUserPaymentMethods($this);
    }

    /**
     * @return HasMany<Event, $this>
     */
    public function credits(): HasMany
    {
        return $this->hasMany(Event::class, 'model_id', 'id')
            ->where('model_type', User::class)
                ->where(function ($q) {
                    return $q->where('event_id', 'credit_applied')
                        ->orWhere('event_id', 'credit_removed');
                })
            ->orderBy('created_at', 'desc');
    }

    public function setPasswordAttribute(?string $value): void
    {
        $this->attributes['password'] = strlen($value) > 0 ? bcrypt($value) : '';
    }

    public function setPricingGroupIdAttribute($value): void
    {
        if (!$value) {
            $this->attributes['pricing_group_id'] = null;
        } else {
            $this->attributes['pricing_group_id'] = $value;
        }
    }

    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getFullNameWithEmailAttribute(): string
    {
        return $this->full_name . ' (' . $this->email . ')';
    }

    public function getRoleAttribute(): ?string
    {
        return UserRole::get($this->role_id);
    }

    public function upcomingOrder(): ?Model
    {
        return $this->orders()
            ->with(['discounts', 'pickup.fees', 'fees', 'cartItems.product', 'date'])
            ->where('is_recurring', true)
            ->latest()
            ->first();
    }

    /**
     * @return HasMany<Order, $this>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'customer_id');
    }

    public function canReceiveSmsMarketing(): bool
    {
        return !empty($this->cell_phone) && !is_null($this->subscribed_to_sms_marketing_at);
    }

    public function decrementOrderCount(bool $recurring = false): User
    {
        if ($recurring) {
            return $this->decrementRecurringOrderCount();
        }

        if ($this->order_count < 1) {
            return $this;
        }

        $this->decrement('order_count');

        return $this;
    }

    public function decrementRecurringOrderCount(): User
    {
        if ($this->recurring_order_count < 1) {
            return $this;
        }

        $this->decrement('recurring_order_count');

        return $this;
    }

    public function getOrderIdAttribute(): ?int
    {
        return $this->currentOrder()?->id;
    }

    public function priceGroupId(): ?int
    {
        if ($this->pricing_group_id) {
            return $this->pricing_group_id;
        }

        if ($this->pickup?->pricing_group_id) {
            return $this->pickup->pricing_group_id;
        }

        return null;
    }

    public function formattedSMSNumber(): ?string
    {
        if ( ! $this->cell_phone &&  ! $this->phone) {
            return null;
        }

        try {
            return app(PhoneNumberService::class)
                ->format($this->cell_phone ?: $this->phone);

        } catch (\InvalidArgumentException $exception) {
            return null;
        }
    }

    public function getIsSubscriberAttribute(): bool
    {
        return $this->recurringOrder()->exists();
    }

    public function isStripeCustomer(): bool
    {
        return ! empty($this->customer_id);
    }

    /**
     * @return HasMany<Post, $this>
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    /**
     * @return HasOne<Profile, $this>
     */
    public function profile(): HasOne
    {
        return $this->hasOne(Profile::class);
    }

    public function hasAProfile()
    {
        return ! is_null($this->profile);
    }

    public function authorSlug(): ?string
    {
        return str("{$this->first_name} {$this->last_name}")->slug();
    }

    public function updateAddress(array $address): User
    {
        $this->street = $address['street'];
        $this->street_2 = $address['street_2'];
        $this->city = $address['city'];
        $this->state = $address['state'];
        $this->zip = $address['zip'];
        $this->country = $address['country'];

        $this->save();

        if ($this->hasAddressAttributes()) {
            $address = Address::firstOrCreate([
                'street' => $this->street,
                'city' => $this->city,
                'state' => $this->state,
                'postal_code' => $this->zip,
                'country' => $this->country,
            ]);

            if ($this->addresses()->where('address_id', $address->id)->wherePivot('street_2', $this->street_2 ?? '')->doesntExist()) {
                $this->addresses()->attach($address->id, [
                    'name' => Str::limit( "{$this->street}, {$this->city}, {$this->state}, {$this->zip}"),
                    'street_2' => $this->street_2 ?? '',
                    'is_default' => $this->addresses()->doesntExist(),
                ]);
            }
        }

        return $this;
    }

    /**
     * @return MorphToMany<Address, $this, MorphPivot, 'location'>
     */
    public function addresses(): MorphToMany
    {
        return $this->morphToMany(Address::class, 'addressable')
            ->withPivot('name', 'street_2', 'is_default', 'instructions')
            ->as('location')
            ->withTimestamps();
    }

    public function updateDefaultShippingAddress(array $shipping_attributes)
    {
        $current_default_address = $this->defaultShippingAttributes();

        // Check if the updated shipping attributes already matches the existing default
        if (
            ! is_null($shipping_attributes['address_id'] ?? null)
            && $current_default_address['address_id'] === $shipping_attributes['address_id']
        ) {
            return;
        }

        $new_default_address = Address::firstOrCreate([
            'street' => $shipping_attributes['street'],
            'city' => $shipping_attributes['city'],
            'state' => $shipping_attributes['state'],
            'postal_code' => $shipping_attributes['postal_code'] ?? $shipping_attributes['zip'] ?? '',
            'country' => $shipping_attributes['country'] ?? app(SettingsService::class)->farmCountry(),
        ]);

        if (! is_null($current_default_address['address_id'] ?? null)) {
            $this->addresses()->updateExistingPivot($current_default_address['address_id'], ['is_default' => false]);
        }

        if ($this->addresses()->where('address_id', $new_default_address->id)->exists()) {
            $this->addresses()->detach($new_default_address->id);
        }

        $this->addresses()->attach($new_default_address->id, [
            'name' => Str::limit( "{$new_default_address->street}, {$new_default_address->city}, {$new_default_address->state}, {$new_default_address->postal_code}"),
            'street_2' => $shipping_attributes['street_2'] ?? '',
            'is_default' => true,
        ]);
    }

    public function defaultShippingAttributes(): array
    {
        return once(function () {
            if ($this->hasAddressAttributes() && $this->addresses()->doesntExist()) {
                $this->migrateUserAddress();
            }

            /** @var Address|null $default_address */
            $default_address = $this->addresses()->default()->first();

            return [
                'address_id' => $default_address->id ?? null,
                'street' => $default_address->street ?? $this->street,
                'street_2' => $default_address->location->street_2 ?? $this->street_2,
                'city' => $default_address->city ?? $this->city,
                'state' => $default_address->state ?? $this->state,
                'postal_code' => $default_address->postal_code ?? $this->zip ?? '',
                'country' => $default_address->country ?? $this->country ?? 'USA',
            ];
        });
    }

    public function hasAddressAttributes(): bool
    {
        return !empty($this->street)
            && !empty($this->city)
            && !empty($this->state)
            && !empty($this->zip);
    }

    public function migrateUserAddress(): void
    {
        $address = Address::firstOrCreate([
            'street' => $this->street,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->zip,
            'country' => app(SettingsService::class)->farmCountry(),
        ]);

        if (
            $this->addresses()
                ->where('address_id', $address->id)
                ->wherePivot('street_2', $this->street_2 ?? '')
                ->doesntExist()
        ) {
            $this->addresses()->attach($address->id, [
                'name' => Str::limit("{$this->street}, {$this->city}, {$this->state} {$this->zip}"),
                'street_2' => $this->street_2 ?? '',
                'is_default' => $this->addresses()->doesntExist(),
            ]);
        }
    }

    public function initials(): string
    {
        return Str::upper(Str::substr($this->first_name ?? '', 0, 1))
            . Str::upper(Str::substr($this->last_name ?? '', 0, 1));
    }

    protected function casts(): array
    {
        return [
            'last_purchase' => 'datetime',
            'last_login' => 'datetime',
            'active' => 'boolean',
            'newsletter' => 'boolean',
            'subscribed_to_sms_marketing_at' => 'datetime'
        ];
    }
}
