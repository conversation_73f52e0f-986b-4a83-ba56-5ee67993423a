<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ProductPriceGroup
 *
 * @property int $id
 * @property string $title
 * @property int $type
 * @property int|null $amount
 * @property string|null $created_at
 * @property string|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPriceGroup whereUpdatedAt($value)
 * @method static \Database\Factories\ProductPriceGroupFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class ProductPriceGroup extends Model
{
    use HasFactory;

    public const FIXED = 0;
    public const PERCENTAGE_INCREASE = 1;
    public const PERCENTAGE_DECREASE = 2;

    public $timestamps = false;

    protected $guarded = [];

    public function isFixed(): bool
    {
        return $this->type === self::FIXED;
    }

    public function isPercentageIncrease(): bool
    {
        return $this->type === self::PERCENTAGE_INCREASE;
    }

    public function isPercentageDecrease(): bool
    {
        return $this->type === self::PERCENTAGE_DECREASE;
    }
}
