<?php

namespace App\Contracts;

use App\Billing\Gateway\Customer;
use App\Billing\Gateway\GatewayException;
use App\Billing\Gateway\PaymentMethod;
use App\Billing\Gateway\Transaction;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderPayment;
use App\Models\User;
use Illuminate\Support\Collection;

interface Billing
{
    public function isConnected(): bool;

    /** @throws GatewayException */
    public function chargeOrderWithCard(Order $order, Card $card, int $amount, string $description): Transaction;

    /** @throws GatewayException */
    public function chargeAmountWithCard(Card $card, int $amount, string $description): Transaction;

    /** @throws GatewayException */
    public function refundPayment(OrderPayment $payment, int $amount = null, string $reason = null): Transaction;

    /**
     * @return Collection<int, PaymentMethod>
     * @throws GatewayException
     */
    public function retrieveUserPaymentMethods(User $user): Collection;

    /** @throws GatewayException */
    public function retrieveCustomerSource(string $customerId, string $sourceId): ?PaymentMethod;

    /** @throws GatewayException */
    public function createCustomer(User $user): Customer;

    /** @throws GatewayException */
    public function createCustomerSource(User $user, string $token, array $params = []): PaymentMethod;

    /** @throws GatewayException */
    public function retrieveDefaultSourceForUser(User $user): ?PaymentMethod;

    /** @throws GatewayException */
    public function setDefaultCard(Card $card): PaymentMethod;

    /** @throws GatewayException */
    public function updateCustomer(string $customerId, Customer $customer): Customer;

    /** @throws GatewayException */
    public function updateCardDetails(Card $card, PaymentMethod $details): PaymentMethod;

    public function createCard(User $user, string $paymentMethodId): PaymentMethod;

    /** @throws GatewayException */
    public function deleteCard(string $customer_id, string $source_id): void;

    public function createSetupIntent(User $user);

    public function fetchSetupIntent(string $id);

    public function fetchPaymentMethod(string $id);

    public function capturePaymentIntent(string $payment_intent_id);
}
