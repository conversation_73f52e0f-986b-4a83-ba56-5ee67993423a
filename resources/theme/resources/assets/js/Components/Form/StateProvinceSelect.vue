<template>
    <div>
        <div class="tw-relative tw-rounded-md tw-shadow-sm">
            <select
                :name="name"
                :id="id"
                :class="[
                    'tw-block tw-w-full focus:tw-outline-none sm:tw-text-sm tw-rounded-md',
                    hasError
                    ? 'tw-pr-10 tw-border-red-300 tw-text-red-900 tw-placeholder-red-300 focus:tw-ring-red-500 focus:tw-border-red-500'
                    : 'tw-border-gray-300 tw-text-gray-700 tw-placeholder-gray-300 focus:tw-ring-theme-action-color focus:tw-border-theme-action-color'
                ]"
                :placeholder="placeholder"
                :value="modelValue"
                @input="$emit('update:modelValue', $event.target.value)"
                :aria-invalid="hasError"
                :aria-describedby="errorId"
                v-bind="$attrs"
            >
                <option v-if="placeholder" :value="null" v-text="placeholder"></option>
                <optgroup label="United States">
                    <option v-for="(state, index) in states" :key="index" :value="state.value" v-text="state.label"></option>
                </optgroup>
                <optgroup label="Canadian Provinces">
                    <option v-for="(province, index) in provinces" :key="index" :value="province.value" v-text="province.label"></option>
                </optgroup>
            </select>
            <div v-if="hasError" class="tw-absolute tw-inset-y-0 tw-right-0 tw-pr-3 tw-flex tw-items-center tw-pointer-events-none">
                <ExclamationCircleIcon class="tw-h-5 tw-w-5 tw-text-red-500" aria-hidden="true" />
            </div>
        </div>
        <p v-if="hasError" class="tw-mt-2 tw-text-sm tw-text-red-600" :id="errorId" v-text="error"></p>
    </div>
</template>

<script>
import { computed } from "vue";
import { ref } from "vue";
import { ExclamationCircleIcon } from '@heroicons/vue/outline'


export default {
    name: "StateProvinceSelect",

    emits: ['update:modelValue'],

    props: {
        modelValue: { required: true },
        id: { required: true },
        name: { required: true },
        error: { default: null },
        placeholder: { default: '' }
    },

    components: {
        ExclamationCircleIcon
    },

    setup(props) {
        const hasError = computed(() => !!props.error)

        const errorId = computed(() => hasError ? `${props.id}-error` : undefined)

        const states = ref([
            { value: 'AL', label: 'Alabama' },
            { value: 'AK', label: 'Alaska' },
            { value: 'AZ', label: 'Arizona' },
            { value: 'AR', label: 'Arkansas' },
            { value: 'CA', label: 'California' },
            { value: 'CO', label: 'Colorado' },
            { value: 'CT', label: 'Connecticut' },
            { value: 'DE', label: 'Delaware' },
            { value: 'DC', label: 'District Of Columbia' },
            { value: 'FL', label: 'Florida' },
            { value: 'GA', label: 'Georgia' },
            { value: 'HI', label: 'Hawaii' },
            { value: 'ID', label: 'Idaho' },
            { value: 'IL', label: 'Illinois' },
            { value: 'IN', label: 'Indiana' },
            { value: 'IA', label: 'Iowa' },
            { value: 'KS', label: 'Kansas' },
            { value: 'KY', label: 'Kentucky' },
            { value: 'LA', label: 'Louisiana' },
            { value: 'ME', label: 'Maine' },
            { value: 'MD', label: 'Maryland' },
            { value: 'MA', label: 'Massachusetts' },
            { value: 'MI', label: 'Michigan' },
            { value: 'MN', label: 'Minnesota' },
            { value: 'MS', label: 'Mississippi' },
            { value: 'MO', label: 'Missouri' },
            { value: 'MT', label: 'Montana' },
            { value: 'NE', label: 'Nebraska' },
            { value: 'NV', label: 'Nevada' },
            { value: 'NH', label: 'New Hampshire' },
            { value: 'NJ', label: 'New Jersey' },
            { value: 'NM', label: 'New Mexico' },
            { value: 'NY', label: 'New York' },
            { value: 'NC', label: 'North Carolina' },
            { value: 'ND', label: 'North Dakota' },
            { value: 'OH', label: 'Ohio' },
            { value: 'OK', label: 'Oklahoma' },
            { value: 'OR', label: 'Oregon' },
            { value: 'PA', label: 'Pennsylvania' },
            { value: 'RI', label: 'Rhode Island' },
            { value: 'SC', label: 'South Carolina' },
            { value: 'SD', label: 'South Dakota' },
            { value: 'TN', label: 'Tennessee' },
            { value: 'TX', label: 'Texas' },
            { value: 'UT', label: 'Utah' },
            { value: 'VT', label: 'Vermont' },
            { value: 'VA', label: 'Virginia' },
            { value: 'WA', label: 'Washington' },
            { value: 'WV', label: 'West Virginia' },
            { value: 'WI', label: 'Wisconsin' },
            { value: 'WY', label: 'Wyoming' },
            { value: 'PR', label: 'Puerto Rico' },
        ])

        const provinces = ref([
            { value: 'NL', label: 'Newfoundland' },
            { value: 'PE', label: 'Prince Edward Island' },
            { value: 'NS', label: 'Nova Scotia' },
            { value: 'NB', label: 'New Brunswick' },
            { value: 'QB', label: 'Quebec' },
            { value: 'ON', label: 'Ontario' },
            { value: 'MB', label: 'Manitoba' },
            { value: 'SK', label: 'Saskatchewan' },
            { value: 'AB', label: 'Alberta' },
            { value: 'BC', label: 'British Columbia' },
            { value: 'YT', label: 'Yukon' },
            { value: 'NT', label: 'Northwest Territories' },
            { value: 'NU', label: 'Nunavut' }
        ])

        return {
            hasError,
            errorId,
            states,
            provinces
        }
    }
};
</script>

