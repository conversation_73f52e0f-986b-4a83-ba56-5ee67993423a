@php
    /** @var \App\Models\RecurringOrder $subscription */
    /** @var bool $show_promotion_change */
    /** @var bool $can_be_modified */
    /** @var \Illuminate\Support\Collection $available_promotion_products */

    $delivery_method = $subscription->fulfillment;
    $oneTimeItems = $subscription->oneTimeItems();
    $subscriptionItems = $subscription->subscriptionItems();
    $promo_item = $subscription->promoItem();
@endphp

<section>
    <ul role="list" class="tw-divide-y tw-divide-gray-200">
        <div class="">
            <div class="tw-py-2 tw-flex tw-items-baseline">
                <p class="tw-m-0 tw-text-gray-900 tw-font-bold tw-text-sm tw-uppercase tw-tracking-wide">Products</p>
                <p class="tw-m-0 tw-ml-2 tw-text-gray-500 tw-text-sm">Repeat every order</p>
            </div>
            <ul role="list">
                @if($promo_item)
                    <li>
                        <x-theme::product-incentive-item :key="$promo_item->id" :item="$promo_item" :show_promotion_change="$show_promotion_change && $can_be_modified" :available_promotion_products="$available_promotion_products" />
                    </li>
                @endif
                @foreach($subscriptionItems as $item)
                    <li>
                        <x-theme::subscription-item :key="$item->id" :item="$item" :can_be_modified="$can_be_modified" />
                    </li>
                @endforeach
            </ul>
        </div>

        @if($oneTimeItems->isNotEmpty())
            <div class="tw-pt-6">
                <div class="tw-pb-1 tw-flex tw-items-baseline">
                    <p class="tw-m-0 tw-text-gray-900 tw-font-bold tw-text-sm tw-uppercase tw-tracking-wide">Add-ons</p>
                    <p class="tw-m-0 tw-ml-2 tw-text-gray-500 tw-text-sm">Upcoming order only</p>
                </div>
                @foreach($oneTimeItems as $item)
                    <li>
                        <x-theme::subscription-item :key="$item->id" :item="$item" :can_be_modified="$can_be_modified" />
                    </li>
                @endforeach
            </div>
        @endif
    </ul>
</section>


