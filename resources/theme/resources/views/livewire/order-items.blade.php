@php
    /** @var \App\Models\Order $order */
    /** @var \Illuminate\Support\Collection $available_promotion_products */

    $delivery_method = $order->pickup;

    $is_from_blueprint = $has_subscription && $order->isFromBlueprint();
    $promo_item = null;
    if ($is_from_blueprint) {
        $oneTimeItems = $order->oneTimeItems();
        [$subscriptionItems, $promo_items] = $order->subscriptionItems()
            ->partition(fn(\App\Models\OrderItem $item) => $item->type !== 'promo');
    }
@endphp

<section>
    <div class="tw-space-y-6">
        @if( ! $is_from_blueprint)
            <ul role="list" class="tw-divide-y tw-divide-gray-200">
                @foreach($order->items()->with('product')->get() as $item)
                    <li>
                        <x-theme::order-item :key="$item->id" :item="$item" :can_be_modified="$order->canBeModified()" />
                    </li>
                @endforeach
            </ul>
        @else
            <div>
                <div class="tw-py-2 tw-flex tw-items-baseline">
                    <p class="tw-m-0 tw-text-gray-900 tw-font-bold tw-text-sm tw-uppercase tw-tracking-wide">Products</p>
                    <p class="tw-m-0 tw-ml-2 tw-text-gray-500 tw-text-sm">Repeat every order</p>
                </div>

                <ul role="list" class="tw-divide-y tw-divide-gray-200">
                    @foreach($promo_items as $item)
                        <li>
                            <x-theme::product-incentive-item :key="$item->id" :item="$item" :show_promotion_change="$order->canBeModified()" :available_promotion_products="$available_promotion_products" />
                        </li>
                    @endforeach
                    @foreach($subscriptionItems as $item)
                        <li>
                            <x-theme::order-item :key="$item->id" :item="$item" :can_be_modified="$order->canBeModified()" />
                        </li>
                    @endforeach
                </ul>
            </div>
            @if($oneTimeItems->isNotEmpty())
                <div class="tw-border-t tw-border-gray-300 tw-pt-6">
                    <div class="tw-pt-2 tw-flex tw-items-baseline">
                        <p class="tw-m-0 tw-text-gray-900 tw-font-bold tw-text-sm tw-uppercase tw-tracking-wide">Add-ons</p>
                        <p class="tw-ml-2 tw-text-gray-500 tw-text-sm">This order only</p>
                    </div>
                    <ul role="list" class="tw-divide-y tw-divide-gray-200">
                        @foreach($oneTimeItems as $item)
                            <li>
                                <x-theme::order-item :key="$item->id" :item="$item" :can_be_modified="$order->canBeModified()" />
                            </li>
                        @endforeach
                    </ul>
                </div>
            @endif
        @endif
    </div>
</section>


