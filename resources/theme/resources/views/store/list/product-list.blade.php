@php
	/** @var \App\Models\Order|null $order */
    /** @var \App\Contracts\Cartable $cart */
    /** @var Illuminate\Support\Collection $products */
    /** @var bool $has_subscription */

 	$is_brand_style = str(theme('store_button_style', 'btn-brand'))->contains('brand');

     // Spelling out all classes here so that they are picked up by the tailwind compiler
	$callout_border_class = ! $is_brand_style ? 'tw-border-theme-brand-color/70' : 'tw-border-theme-action-color/70';
	$background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color' : 'tw-bg-theme-action-color';
	$light_background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color/5' : 'tw-bg-theme-action-color/5';
	$text_class = ! $is_brand_style ? 'tw-text-theme-brand-color' : 'tw-text-theme-action-color';
@endphp

<div class="tw-space-y-4 md:tw-space-y-8 xl:tw-space-y-10">
	@foreach($products as $product)
		@php /** @var \App\Models\Product $product */ @endphp

		<!-- Start {{ $product->title }} -->
		@include('theme::store.list.product', ['product' => $product, 'type' => 'retail'])
		<!-- End {{ $product->title }} -->

	@endforeach
</div>
