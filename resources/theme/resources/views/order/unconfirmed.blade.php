@if($order->orderWindowIsOpen())
	<div class="form-group">
		<a href="{{ session('store_url', '/store') }}">
			<i class="fa fa-chevron-left"></i> {{ __('messages.cart.keep_shopping') }} </a>
	</div>
	<div class="form-group visible-xs">
		@if($order->meetsOrderMinimum())
			<a href="/checkout" class="btn btn-action btn-block">@lang('messages.cart.checkout_cta') <i class="fa fa-chevron-right"></i></a>
		@else
			<span class="label cta">This order does not meet the minimum requirement of &#36;{{ money($order->pickup['min_customer_orders']) }}</span>	
		@endif
	</div>
	@if(count($order->items))
	<div class="row">
		<div class="col-sm-8">
			@include('theme::order.partials.order-items-open')
			@if($order->meetsOrderMinimum())
				<div class="panel panel-default hidden-xs">
		            <div class="panel-body">
		                <a href="/checkout" class="btn btn-action">@lang('messages.cart.checkout_cta') <i class="fa fa-chevron-right"></i></a>
		            </div>
		        </div>    
            @endif
        </div>
		<div class="col-sm-4">
			<div class="summaryContainer">
				<div class="panel panel-default" style="max-width: 325px; margin-left: auto;">
					<div class="panel-body">
						@if($order->meetsOrderMinimum())
							<a href="/checkout" class="btn btn-action btn-block">@lang('messages.cart.checkout_cta') <i class="fa fa-chevron-right"></i></a>
						@else
							<a href="/store" class="btn btn-action btn-block">Order minimum of &#36;{{ money($order->pickup['min_customer_orders']) }} required</a>	
						@endif
						<hr>
						<div class="form-group"><strong>Order Summary</strong></div>
						@include('theme::order.partials.summary')
					</div>
					@if(getMessage('order_summary'))
	                    <div class="panel-footer"><small>&ast;{{ getMessage('order_summary') }}</small></div>
	                @endif
				</div>
			</div>		
		</div>
	</div>
	@else
	<div class="panel panel-default">
		<div class="panel-body text-center">
			<div class="form-group">
				<strong>There are no items in your cart...</strong>
			</div>	
			<a href="/store" class="btn btn-default">Start shopping</a></div>
		</div>
	</div>
	@endif
@else
	<div class="alert brand">
		Ordering is currently closed for the <strong>{{ $order->pickup->present()->title() }}</strong> pickup location.
		<a href="/locations/{{ $order->pickup->slug }}">Click here to see the schedule.</a>
	</div>
	<div class="row">
		<div class="col-sm-8">
			@include('theme::order.partials.order-items-closed')
		</div>	

		<div class="col-sm-4 text-right">
			@include('theme::order.partials.summary')
		</div>
	</div>	
@endif