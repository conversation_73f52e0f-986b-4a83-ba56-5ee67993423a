@props(['selected' => null, 'include_all' => false])

@php
    $selected_array = \Illuminate\Support\Arr::wrap($selected);
@endphp

<select {{ $attributes }}>
    @if($include_all)
        <option value="">All</option>
    @endif
    @foreach(\App\Support\Enums\Channel::allByDesc() as $key => $label)
        <option value="{{ $key }}" @selected(in_array($key, $selected_array))>{{ $label }}</option>
    @endforeach
</select>
