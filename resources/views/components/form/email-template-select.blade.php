@props(['selected' => null, 'placeholder' => null, 'placeholder_value' => null, 'disabled' => false])

@php
    $templates = \App\Models\Template::query()
        ->where('type', '=', 'email')
        ->orderBy('title')
        ->pluck('title', 'id');
@endphp

<select {{ $attributes }} @disabled($disabled)>
    @if($placeholder)
        <option @if($placeholder_value) value="{{ $placeholder_value }}" @else value="" @endif>{{ $placeholder }}</option>
    @endif
    @foreach($templates as $id => $title)
        <option value="{{ $id }}" @selected($selected === $id)>{{ $title }}</option>
    @endforeach
</select>
