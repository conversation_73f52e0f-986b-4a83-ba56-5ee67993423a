@if($protocols->count())
<div class="panel ">
    <div class="panel-body pa-0">
        <table class="table table-striped table-full table-list">
            <thead>
            <tr>
                <th>{!! sortTable('Protocol', 'title') !!}</th>
                <th>{!! sortTable('Description', 'description') !!}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($protocols as $protocol)
                <tr>
                    <td width="25%"><a href="{{ route('admin.protocols.edit', $protocol->id) }}">{{ $protocol->title }}</a></td>
                    <td>{!! $protocol->description !!}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
</div>
@else
<div class="row">
    <div class="content">
        <div class="panel ">
            <div class="panel-body">
                <h4><strong>What are Protocols?</strong></h4>
                <div class="form-group">
                    <p>Protocols are a great way to inform your customers about how your food is raised & prepared.</p>
                    <p>Some examples of protocols would be: Grass-fed, None-GMO, Drug-Free, etc.<p>
                        <p>A protocols page will be dynamically populated with any protocols you add. You can add this page to your menus via the menu editor.
                        Any protocols associated with a product will also show on that product's details page.</p>
                    <p>You can assign multiple protocols to a product under the product's "Protocols" tab.</p>
                </div>
                <a href="#" data-toggle="modal" data-target="#createProtocolModal" class="btn btn-success">Add Protocol</a>
            </div>
        </div>
    </div>
</div>
@endif