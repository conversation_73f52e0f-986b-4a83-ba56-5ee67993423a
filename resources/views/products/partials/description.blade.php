<form action="{{ route('admin.products.description.update', compact('product')) }}" method="POST" id="productForm">
    @csrf
    @method('PUT')
    <div class="panel panel-tabs">
        <div class="panel-body">
            <div class="form-group">
                <label for="title" class="block text-sm font-medium text-gray-700">Name</label>
                <input type="text" name="title" class="form-control" value="{{ old('title', $product->title) }}"/>
            </div>

            <div class="form-group">
                <label for="unit_description" class="block text-sm font-medium text-gray-700">Unit Description</label>
                <input type="text" name="unit_description" class="form-control" value="{{ old('unit_description', $product->unit_description) }}"/>
            </div>
            <div class="form-group">
                <label for="fulfillment_instructions" class="block text-sm font-medium text-gray-700">Fulfillment Instructions</label>
                <input type="text" name="fulfillment_instructions" class="form-control" value="{{ old('fulfillment_instructions', $product->fulfillment_instructions) }}"/>
                <p class="m-0 mt-2 text-gray-500 text-sm">When present, this is shown on the packing list instead of the product title.</p>
            </div>

            <div class="mt-8 form-group">
                <div class="flex justify-between">
                    <label for="summary" class="block text-sm font-medium text-gray-700">Summary</label>
                    <span class="text-sm text-gray-500">Max 1000</span>
                </div>
                <text-editor class="prose max-w-full" name="summary" :disable-media="true" :character-limit="1000" :rows="2" min-height="200px" content="{{ old('summary', $product->summary) }}"></text-editor>
            </div>

            <div class="form-group">
                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                <text-editor class="prose max-w-full" name="description" :rows="10" content="{{ old('description', $product->description) }}"></text-editor>
            </div>

            <div class="form-group">
                <label for="ingredients" class="block text-sm font-medium text-gray-700">Ingredients</label>
                <textarea name="ingredients" rows="5" class="form-control">{{ old('ingredients', $product->ingredients) }}</textarea>
            </div>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('productForm')">Save</button>
        </div>
    </div>
</form>	
