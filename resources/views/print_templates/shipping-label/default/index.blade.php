@extends('print_templates.layout')

@section('content')
    <?php /** @var \App\Order $order */ ?>

    @foreach($orders as $order)
        @for($i = 0; $i < max(1, $order->containers + $order->containers_2); $i++)
            <div class="label-container">
                <div class="customer-name">SHIP TO:</div>
                <table>
                    <tr>
                        <td>
                            <ul>
                                <li><strong>{{ $order['customer']['company_name'] ?? '' }}</strong></li>
                                <li>{{ $order->customer_first_name.' '.$order->customer_last_name }}</li>
                                <li>{{ $order->shipping_street }}</li>
                                <li>{{ $order->shipping_street_2 }}</li>
                                <li>{{ $order->shipping_city.' '.$order->shipping_state.', '.$order->shipping_zip }}</li>
                                <li><em>{{ $order['pickup']['title'] ?? '' }}</em></li>
                            </ul>
                        </td>
                        <td>
                        <ul>
                            <li>
                                @if($order->containers)
                                    Frozen Boxes: {{ $order->containers }}
                                @else
                                    Frozen Boxes: ___
                                @endif
                            </li>
                            <li>
                                @if($order->containers_2)
                                    Fresh Boxes: {{ $order->containers_2 }}
                                @else
                                    Fresh Boxes: ___
                                @endif
                            </li>
                        </td>
                    </tr>
                </table>
                <hr>
                <div class="customer-name">SHIP FROM:</div>
                <ul>
                    <li>{{ setting('farm_name') }}</li>
                    <li>{{ setting('farm_street').', '.setting('farm_city').' '.setting('farm_state').', '.setting('farm_zip') }}</li>
                </ul>
            </div>
        @endfor
    @endforeach
@stop