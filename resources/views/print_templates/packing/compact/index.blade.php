@extends('print_templates.layout')

@section('styles')
.packing-list {
    page-break-after: {{ setting('layout_page_break', 'always') }};
    break-after: {{ setting('layout_page_break', 'always') }};
}
@stop

@section('content')
@if($orders->count())
    @foreach($orders as $order)
        <div class="packing-list">
            <div class="packing-list__container">
                <div class="order-info">
                    <ul>
                        <li class="bold">Order # {{ $order->id }}</li>
                        <li>
                            <span class="fas fa-star __{{ $order->first_time_order }}">&#9733;</span>
                            {{ $order->customer->last_name }} {{ $order->customer->first_name }}
                        </li>
                        <li class="word-wrap">{{ $order->customer->email }}</li>
                        <li>{{ $order->customer->phone }}</li>
                        <li>&#36;{{ money($order->total) }} / {{ weight($order->weight) }}</li>
                        <li>{{ $order->pickup->title }} - {{ $order->present()->pickupDate('m/d/y') }}</li>
                    </ul>    

                    @if($order->customer_notes)
                        <div class="customerNotes">{{ $order->customer_notes }}</div>
                    @endif
                    @if($order->packing_notes)
                        <div class="packingNotes">{{ $order->packing_notes }}</div>
                    @endif
                </div>

                <div class="order-items">
                    <table>
                        @foreach($order->items as $item)
                            @if($item->is_grouped)
                                @include('print_templates.packing.compact._item_row')  
                            @else
                                @for($i = 0; $i < $item->fulfilledQuantity(); $i++)
                                    @include('print_templates.packing.compact._item_row', [
                                        'qty' => 1,
                                        'weight' => $item->weight / $item->qty
                                    ])
                                @endfor 
                            @endif
                        @endforeach
                    </table>
                </div>
            </div>
            <div class="footer">
                <div class="flex-fill"><hr></div>
                <div class="flex">End of Order {{ $order->id }}</div>
                <div class="flex-fill"><hr></div>
            </div>
        </div> 
    @endforeach
@else
    <p><b>No orders found.</b></p>
@endif
@stop