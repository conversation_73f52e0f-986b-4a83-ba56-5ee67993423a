<div>
    <h2 class="text-base/7 font-semibold text-gray-900">Bundle Template Settings</h2>
    <p class="mt-1 text-sm/6 text-gray-600">Configure how the bundle template is displayed.</p>

    <div class="border-b border-gray-900/10 pb-12">
        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div class="col-span-full">
                <div class="flex justify-between">
                    <label for="heading" class="block text-sm/6 font-medium text-gray-900">Heading</label>
                    <span class="text-sm/6 text-gray-500" id="heading-html-support">Supports HTML</span>
                </div>
                <div class="relative mt-2 rounded-md shadow-sm">
                    <textarea id="heading"
                              wire:model="heading"
                              name="heading"
                              rows="3"
                              class="block w-full rounded-md shadow-sm border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('heading') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                              @error('heading') aria-invalid="true" @enderror
                              aria-describedby="heading-html-support"
                    ></textarea>

                    @error('heading')
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <!-- Heroicon name: mini/exclamation-circle -->
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </div>
                    @enderror
                </div>
                @error('heading')
                <p class="m-0 mt-2 text-sm text-red-600" id="heading-error">{{ $message }}</p>
                @enderror
            </div>

            <div class="col-span-full">
                <div class="flex justify-between">
                    <label for="subheading" class="block text-sm/6 font-medium text-gray-900">Subheading</label>
                    <span class="text-sm/6 text-gray-500" id="subheading-html-support">Supports HTML</span>
                </div>
                <div class="relative mt-2 rounded-md shadow-sm">
                    <textarea id="subheading"
                              wire:model="subheading"
                              name="subheading"
                              rows="3"
                              class="block w-full rounded-md shadow-sm border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('subheading') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                              @error('subheading') aria-invalid="true" @enderror
                              aria-describedby="subheading-html-support"
                    ></textarea>

                    @error('subheading')
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <!-- Heroicon name: mini/exclamation-circle -->
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </div>
                    @enderror
                </div>
                @error('subheading')
                <p class="m-0 mt-2 text-sm text-red-600" id="subheading-error">{{ $message }}</p>
                @enderror
            </div>

            <div class="col-span-full">
                <div class="flex justify-between">
                    <label for="video_embed" class="block text-sm/6 font-medium text-gray-900">Video Embed</label>
                    <span class="text-sm/6 text-gray-500" id="video_embed-html-support">Supports HTML</span>

                </div>
                <div class="relative mt-2 rounded-md shadow-sm">
                    <textarea id="video_embed"
                              wire:model="video_embed"
                              name="video_embed"
                              rows="3"
                              class="block w-full rounded-md shadow-sm border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('video_embed') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                              @error('video_embed') aria-invalid="true" @enderror
                              aria-describedby="video_embed-html-support"
                    ></textarea>

                    @error('video_embed')
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <!-- Heroicon name: mini/exclamation-circle -->
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </div>
                    @enderror
                </div>
                @error('video_embed')
                <p class="m-0 mt-2 text-sm text-red-600" id="video_embed-error">{{ $message }}</p>
                @enderror
                <p class="mt-3 text-sm/6 text-gray-600">Paste the embed code copied from Tolstoy.</p>
            </div>

            <div class="col-span-full">
                <div class="flex justify-between">
                    <label for="contents_description" class="block text-sm/6 font-medium text-gray-900">Bundle Contents</label>
                    <span class="text-sm/6 text-gray-500" id="contents_description-html-support">Supports HTML</span>
                </div>
                <div class="relative mt-2 rounded-md shadow-sm">
                    <textarea id="contents_description"
                              wire:model="contents_description"
                              name="contents_description"
                              rows="5"
                              class="block w-full rounded-md shadow-sm border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('contents_description') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                              @error('contents_description') aria-invalid="true" @enderror
                              aria-describedby="contents_description-html-support"
                    ></textarea>

                    @error('contents_description')
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <!-- Heroicon name: mini/exclamation-circle -->
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                             viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </div>
                    @enderror
                </div>
                @error('contents_description')
                <p class="m-0 mt-2 text-sm text-red-600" id="contents_description-error">{{ $message }}</p>
                @enderror
                <p class="mt-3 text-sm/6 text-gray-600">Describe what products are contained in the bundle.</p>
            </div>

            <div class="col-span-full">
                <label for="farming_practices" class="block text-sm/6 font-medium text-gray-900">Farming Practices</label>
                <div class="relative mt-2 rounded-md w-56 shadow-sm">
                    <select wire:model="farming_practices" id="farming_practices" name="farming_practices" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-theme-action-color sm:text-sm @error('farming_practices') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="farming_practices-error">
                        <option value="beef">Beef</option>
                        <option value="chicken">Chicken</option>
                        <option value="pork">Pork</option>
                        <option value="seafood">Seafood</option>
                        <option value="lamb">Lamb</option>
                        <option value="bison">Bison</option>
                        <option value="mixed">Mixed</option>
                    </select>
                    @error('farming_practices')
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <!-- Heroicon name: mini/exclamation-circle -->
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    @enderror
                </div>
                @error('farming_practices')
                <p class="m-0 mt-2 text-sm text-red-600" id="farming_practices-error">{{ $message }}</p>
                @enderror
            </div>

            <div class="col-span-full sm:col-span-3">
                <label for="related_products" class="block text-sm/6 font-medium text-gray-900">Related Products</label>
                <div class="relative mt-2 rounded-md shadow-sm">
                    <input type="text" x-ref="autocompleteGoogle" wire:model="related_products" name="related_products" id="related_products" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('related_products') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="related_products-error">
                    @error('related_products')
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <!-- Heroicon name: mini/exclamation-circle -->
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    @enderror
                </div>
                @error('related_products')
                <p class="m-0 mt-2 text-sm text-red-600" id="related_products-error">{{ $message }}</p>
                @enderror
                <p class="mt-3 text-sm/6 text-gray-600">Accepts a comma-separated list of product IDs.</p>
            </div>

            <div class="col-span-full sm:col-span-3">
                <label for="related_recipes" class="block text-sm/6 font-medium text-gray-900">Related Recipes</label>
                <div class="relative mt-2 rounded-md shadow-sm">
                    <input type="text" x-ref="autocompleteGoogle" wire:model="related_recipes" name="related_recipes" id="related_recipes" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('related_recipes') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="related_recipes-error">
                    @error('related_recipes')
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <!-- Heroicon name: mini/exclamation-circle -->
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    @enderror
                </div>
                @error('related_recipes')
                <p class="m-0 mt-2 text-sm text-red-600" id="related_recipes-error">{{ $message }}</p>
                @enderror
                <p class="mt-3 text-sm/6 text-gray-600">Accepts a comma-separated list of reciple slugs.</p>
            </div>
        </div>
    </div>

    <div class="mt-6 flex items-center justify-end gap-x-6">
        <button type="button" wire:click="save" class="rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">Save</button>
    </div>
</div>
