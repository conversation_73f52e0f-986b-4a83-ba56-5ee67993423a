<?php

use App\Console\Commands\CheckInventoryLevels;
use App\Console\Commands\ExtendRecurringSchedules;
use App\Console\Commands\GenerateSubscriptionOrders;
use App\Console\Commands\NotifyAdminsOfOutOfStockProducts;
use App\Console\Commands\ProcessMarketingReviews;
use App\Console\Commands\ProcessRecurringOrders;
use App\Console\Commands\RecordNewSubscriptionOrdersInDrip;
use App\Console\Commands\ScheduleReminders;
use App\Console\Commands\SyncSubscriptionReserveInventory;
use App\Console\Commands\TagTopCustomers;
use App\Jobs\BackFillUserAddresses;
use App\Jobs\GenerateSubscriptionDemandView;
use App\Jobs\MigrateCustomerFromClosedDeliveryMethod;
use App\Jobs\ReAssociateDeleteCustomerOrders;
use App\Jobs\SendOrderCreditReport;
use App\Jobs\SendSubscriptionDemandReport;
use App\Jobs\StubCategories;
use App\Jobs\UpdateScheduleDayOfTheWeek;
use App\Models\Category;
use App\Models\Pickup;
use App\Models\Price;
use App\Models\Product;
use App\Models\User;
use App\Support\Enums\PickupStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use Illuminate\Support\Collection;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('grazecart:backfill-user-addresses', function () {

     BackFillUserAddresses::dispatch();

})->purpose('Back fills user addresses');

Artisan::command('grazecart:stub-categories', function () {

    StubCategories::dispatch();

})->purpose('Stub the categories table from subcollections and accounting IDs.');

Artisan::command('grazecart:re-associate-deleted-customers-orders', function () {

    ReAssociateDeleteCustomerOrders::dispatch();

})->purpose('Re-associate deleted customers orders..');

Artisan::command('app:update-schedule-day-of-week', function () {
    \App\Models\Schedule::query()
        ->where('type_id', \App\Models\Schedule::TYPE_REPEATING)
        ->get()
        ->each(fn($schedule) => UpdateScheduleDayOfTheWeek::dispatch($schedule));
});

Artisan::command('app:migrate-customers-from-closed-delivery-methods', function () {
    User::query()
        ->whereIn('pickup_point', Pickup::query()->where('status_id', PickupStatus::CLOSED->value)->pluck('id'))
        ->chunkById(25, function (Collection $users) {
            $users->each(fn(User $user) =>
                MigrateCustomerFromClosedDeliveryMethod::dispatch($user->pickup_point, $user->id)
            );
        });
})
    ->purpose('Re-associate customers to active delivery methods.')
    ->everyFifteenMinutes();

Artisan::command('app:migrate-product-prices', function () {
    Product::withTrashed()->chunk(100, function ($products) {
        $data = $products->map(fn($product) => [
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => $product->unit_price,
            'sale_unit_price' => $product->sale_unit_price,
        ])->toArray();

        Price::upsert($data, ['product_id', 'quantity'], ['unit_price', 'sale_unit_price']);
    });

})->purpose('Migrate product prices to the prices table');

Artisan::command('app:migrate-summary-to-subheading', function () {
    Category::query()
        ->whereNotNull('extra_attributes->summary')
        ->chunkById(100, function ($categories) {
            foreach ($categories as $category) {
                $extraAttributes = $category->extra_attributes;

                if (isset($extraAttributes['summary'])) {
                    $extraAttributes['subheading'] = $extraAttributes['summary'];
                    $category->update(['extra_attributes' => $extraAttributes]);
                }
            }
        });

})->purpose('Migrate summary to subheading in extra_attributes column for categories');

Schedule::command(ScheduleReminders::class)
    ->dailyAt('3:00')
    ->timezone('America/New_York');

Schedule::command(ProcessRecurringOrders::class)
    ->everyThirtyMinutes()
    ->timezone('America/New_York');

Schedule::command(GenerateSubscriptionOrders::class)
    ->everyThirtyMinutes()
    ->timezone('America/New_York');

Schedule::command(RecordNewSubscriptionOrdersInDrip::class)
    ->hourly()
    ->timezone('America/New_York');

Schedule::command(ExtendRecurringSchedules::class)
    ->dailyAt('4:00')
    ->timezone('America/New_York');

Schedule::command(ProcessMarketingReviews::class)
    ->monthlyOn(1, '9:00')
    ->timezone('America/New_York');

Schedule::command('horizon:snapshot')->everyFiveMinutes();

Schedule::command('disposable:update')->weekly();

Schedule::command('cache:prune-stale-tags')->hourly();

Schedule::command(CheckInventoryLevels::class)->everyFifteenMinutes();

Schedule::command(NotifyAdminsOfOutOfStockProducts::class)
    ->everyFiveMinutes()
    ->timezone('America/New_York');

Schedule::command(TagTopCustomers::class, ['200'])
    ->monthlyOn(1);

Schedule::command(SyncSubscriptionReserveInventory::class)
    ->everySixHours();

Schedule::job(new GenerateSubscriptionDemandView)
    ->cron('15,45 * * * *'); // runs at 15 past and 15 til the hour

Schedule::job(SendSubscriptionDemandReport::class)
    ->days([Carbon::MONDAY, Carbon::TUESDAY, Carbon::WEDNESDAY, Carbon::THURSDAY])
    ->everySixHours();

Schedule::job(SendSubscriptionDemandReport::class)
    ->days([Carbon::FRIDAY, Carbon::SATURDAY])
    ->everyThreeHours();

Schedule::job(SendSubscriptionDemandReport::class)
    ->sundays()
    ->hourly();

Schedule::job(SendOrderCreditReport::class)
   ->timezone('America/New_York')
    ->monthlyOn(1,'6:00');
