tinymce.PluginManager.add("colorpicker",function(e){function t(t,n){function r(e){var t=new tinymce.util.Color(e),n=t.toRgb();o.fromJSON({r:n.r,g:n.g,b:n.b,hex:t.toHex().substr(1)}),i(t.toHex())}function i(e){o.find("#preview")[0].getEl().style.background=e}var o=e.windowManager.open({title:"Color",items:{type:"container",layout:"flex",direction:"row",align:"stretch",padding:5,spacing:10,items:[{type:"colorpicker",value:n,onchange:function(){var e=this.rgb();o&&(o.find("#r").value(e.r),o.find("#g").value(e.g),o.find("#b").value(e.b),o.find("#hex").value(this.value().substr(1)),i(this.value()))}},{type:"form",padding:0,labelGap:5,defaults:{type:"textbox",size:7,value:"0",flex:1,spellcheck:!1,onchange:function(){var e,t,n=o.find("colorpicker")[0];return e=this.name(),t=this.value(),"hex"==e?(t="#"+t,r(t),void n.value(t)):(t={r:o.find("#r").value(),g:o.find("#g").value(),b:o.find("#b").value()},n.value(t),void r(t))}},items:[{name:"r",label:"R",autofocus:1},{name:"g",label:"G"},{name:"b",label:"B"},{name:"hex",label:"#",value:"000000"},{name:"preview",type:"container",border:1}]}]},onSubmit:function(){t("#"+this.toJSON().hex)}});r(n)}e.settings.color_picker_callback||(e.settings.color_picker_callback=t)});