<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Order\ApplyCoupon;
use App\Exceptions\CouponInvalidException;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Setting;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ApplyCouponTest extends TenantTestCase
{
    #[Test]
    public function it_throws_an_exception_when_a_coupon_has_already_been_applied_to_an_order(): void
    {
        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();

        $order->discounts()->save($coupon, ['user_id' => $order->customer_id]);

        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('This coupon has already been applied.');

        (new ApplyCoupon)->handle($order, $coupon);
    }

    #[Test]
    public function it_throws_an_exception_when_multiple_coupons_are_not_allowed(): void
    {
        Setting::updateOrCreate(['key' => 'prevent_multiple_coupons'], ['value' => '1']);

        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();
        $new_coupon = Coupon::factory()->create();

        $order->discounts()->save($coupon, ['user_id' => $order->customer_id]);

        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('Only one coupon can be applied at a time.');

        (new ApplyCoupon)->handle($order, $new_coupon);
    }

    #[Test]
    public function it_throws_an_exception_when_coupon_has_expired(): void
    {
        Carbon::setTestNow(now());

        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create(['expires' => true, 'expires_at' => now()->subSecond()]);

        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('This coupon has expired.');

        (new ApplyCoupon)->handle($order, $coupon);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_throws_an_exception_when_it_has_already_been_used_by_the_customer(): void
    {
        $previous_order = Order::factory()->create();
        $coupon = Coupon::factory()->create(['once_per_customer' => true]);

        $previous_order->discounts()->save($coupon, ['user_id' => $previous_order->customer_id]);

        $order = Order::factory()->create(['customer_id' => $previous_order->customer_id]);

        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('You have already redeemed this coupon.');

        (new ApplyCoupon)->handle($order, $coupon);
    }

    #[Test]
    public function it_throws_an_exception_when_coupon_order_minimum_has_not_been_met(): void
    {
        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create(['min_order' => 1, 'min_order_amount' => 100000]);

        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('This coupon can only be applied on orders of &#36;1,000.00 or more.');

        (new ApplyCoupon)->handle($order, $coupon);
    }

    #[Test]
    public function it_can_apply_a_coupon_to_the_order(): void
    {
        $order = Order::factory()->create([
            'coupon_subtotal' => 0,
            'subtotal' => 1230,
            'total' => 1230,
        ]);

        $product = Product::factory()->create(['unit_price' => 12.30]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'unit_price' => $product->unit_price, 'qty' => 1, 'subtotal' =>  $product->unit_price]);
        $coupon = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 1000, 'total_uses' => 1]);

        $result = (new ApplyCoupon)->handle($order, $coupon);

        $this->assertDatabaseHas('coupon_order', [
            'coupon_id' => $coupon->id,
            'order_id' => $order->id,
            'user_id' => $order->customer_id,
            'savings' => $coupon->discount_amount,
        ]);

        $this->assertDatabaseHas(Coupon::class, [
            'id' => $coupon->id,
            'total_uses' => 2,
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'coupon_subtotal' => 1000,
            'subtotal' => 1230,
            'total' => 230,
        ]);

        Carbon::setTestNow();
    }
}