<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\IsBundle;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class IsBundleTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_not_is_bundle(): void
    {
        $collection = (new IsBundle)->handle([$this->createRequest(['is_bundle' => '0']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('is_bundle'));

        /** @var IsBundle $filter */
        $filter = $collection->get('is_bundle');

        $this->assertEquals('No Bundles', $filter->label());
        $this->assertEquals('', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_is_bundle(): void
    {
        $collection = (new IsBundle)->handle([$this->createRequest(['is_bundle' => '1']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('is_bundle'));

        /** @var IsBundle $filter */
        $filter = $collection->get('is_bundle');

        $this->assertEquals('Bundles Only', $filter->label());
        $this->assertEquals('', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}