<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Models\Tag as TagModel;
use App\Services\FilterService\Filters\Tags;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class TagsTest extends TenantTestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_tag(): void
    {
        $tag = TagModel::factory()->create();

        $collection = (new Tags)->handle([$this->createRequest(['tags' => $tag->id]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('tags'));

        /** @var Tags $filter */
        $filter = $collection->get('tags');

        $this->assertEquals('Tags:', $filter->label());
        $this->assertEquals($tag->title, $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_statuses(): void
    {
        $tags = TagModel::factory()->times(2)->create();

        $collection = (new Tags)->handle([$this->createRequest(['tags' => $tags->pluck('id')->toArray()]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('tags'));

        /** @var Tags $filter */
        $filter = $collection->get('tags');

        $this->assertEquals('Tags:', $filter->label());
        $this->assertEquals($tags->pluck('title')->implode(', '), $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}