<?php

namespace Tests\Unit\Jobs;

use App\Jobs\UpdateInconsistentOrderShippingCountryRecords;
use App\Models\Order;
use App\Models\Setting;
use App\Services\SettingsService;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UpdateInconsistentOrderShippingCountryRecordsTest extends TenantTestCase
{
    #[Test]
    public function it_updates_inconsistent_order_shipping_country_records_for_USA(): void
    {
        Setting::updateOrCreate(['key' => 'farm_country'], ['value' => 'USA']);

        $order1 = Order::factory()->create(['shipping_country' => 'USA']);
        $order2 = Order::factory()->create(['shipping_country' => null]);
        $order3 = Order::factory()->create(['shipping_country' => 'Any Country']);
        $order4 = Order::factory()->create(['shipping_country' => 'Canada']);

        (new UpdateInconsistentOrderShippingCountryRecords)->handle();

        $this->assertCount(0, Order::query()
            ->whereNot('shipping_country', app(SettingsService::class)->farmCountry())
            ->orWhereNull('shipping_country')
            ->get());

        $this->assertDatabaseHas(Order::class, [
           'id' => $order1->id,
           'shipping_country' => 'USA',
        ]);

        $this->assertDatabaseHas(Order::class, [
           'id' => $order2->id,
           'shipping_country' => 'USA',
        ]);

        $this->assertDatabaseHas(Order::class, [
           'id' => $order3->id,
           'shipping_country' => 'USA',
        ]);

        $this->assertDatabaseHas(Order::class, [
           'id' => $order4->id,
           'shipping_country' => 'USA',
        ]);
    }

    #[Test]
    public function it_updates_inconsistent_order_shipping_country_records_for_Canada(): void
    {
        Setting::updateOrCreate(['key' => 'farm_country'], ['value' => 'Canada']);

        $order1 = Order::factory()->create(['shipping_country' => 'Canada']);
        $order2 = Order::factory()->create(['shipping_country' => null]);
        $order3 = Order::factory()->create(['shipping_country' => 'Any Country']);
        $order4 = Order::factory()->create(['shipping_country' => 'USA']);
        $order5 = Order::factory()->create(['shipping_country' => 'CAN']);

        (new UpdateInconsistentOrderShippingCountryRecords)->handle();

        $this->assertCount(0, Order::query()
            ->whereNot('shipping_country', app(SettingsService::class)->farmCountry())
            ->orWhereNull('shipping_country')
            ->get());

        $this->assertDatabaseHas(Order::class, [
           'id' => $order1->id,
           'shipping_country' => 'Canada',
        ]);

        $this->assertDatabaseHas(Order::class, [
           'id' => $order2->id,
           'shipping_country' => 'Canada',
        ]);

        $this->assertDatabaseHas(Order::class, [
           'id' => $order3->id,
           'shipping_country' => 'Canada',
        ]);

        $this->assertDatabaseHas(Order::class, [
           'id' => $order4->id,
           'shipping_country' => 'Canada',
        ]);

        $this->assertDatabaseHas(Order::class, [
           'id' => $order5->id,
           'shipping_country' => 'Canada',
        ]);
    }
}
