<?php

namespace Tests\Unit\Jobs\User;

use App\Jobs\User\DeleteCustomer;
use App\Models\Order;
use App\Models\RecurringOrder;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DeleteCustomerTest extends TenantTestCase
{
    #[Test]
    public function it_deletes_the_user_and_assigns_all_orders_and_subscription_to_the_deleted_user_object(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::confirmed(),
            'canceled' => false,
            'canceled_at' => null,
            'confirmed' => true,
        ]);

        $blueprint = RecurringOrder::factory()->create(['customer_id' => $user->id]);
        $recurringOrder = Order::factory()->create([
            'customer_id' => $blueprint->customer_id,
            'status_id' => OrderStatus::confirmed(),
            'is_recurring' => true,
            'confirmed' => true,
            'blueprint_id' => $blueprint->id
        ]);

        (new DeleteCustomer($user->id))->handle();

        $this->assertNull(User::find($user->id));
        $this->assertEquals(User::where('email', '<EMAIL>')->first()->id, $order->fresh()->customer_id);
        $this->assertEquals(User::where('email', '<EMAIL>')->first()->id, $recurringOrder->fresh()->customer_id);
    }
}
