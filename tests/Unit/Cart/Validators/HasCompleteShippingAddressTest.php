<?php

namespace Tests\Unit\Cart\Validators;

use App\Cart\Validators\CartValidationException;
use App\Cart\Validators\HasCompleteShippingAddress;
use App\Models\Cart;
use App\Models\Pickup;
use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class HasCompleteShippingAddressTest extends TenantTestCase
{
    #[Test]
    public function it_passes_validation_when_cart_has_complete_shipping_address_for_delivery_zone(): void
    {
        $user = User::factory()->create();
        $pickup = Pickup::factory()->deliveryZone()->create();
        
        $cart = Cart::factory()->create([
            'shopper_id' => $user->id,
            'pickup_id' => $pickup->id,
        ]);

        $cart->setShippingInfo([
            'street' => '123 Main St',
            'city' => 'Test City',
            'state' => 'TE',
            'zip' => '12345',
            'country' => 'USA',
        ]);

        $validator = new HasCompleteShippingAddress();
        
        $result = $validator->handle($cart, fn($cart) => $cart);
        
        $this->assertSame($cart, $result);
    }

    #[Test]
    public function it_throws_exception_when_cart_has_incomplete_shipping_address_for_delivery_zone(): void
    {
        $user = User::factory()->create();
        $pickup = Pickup::factory()->deliveryZone()->create();
        
        $cart = Cart::factory()->create([
            'shopper_id' => $user->id,
            'pickup_id' => $pickup->id,
        ]);

        // Set incomplete shipping info (missing street)
        $cart->setShippingInfo([
            'street' => '',
            'city' => 'Test City',
            'state' => 'TE',
            'zip' => '12345',
            'country' => 'USA',
        ]);

        $validator = new HasCompleteShippingAddress();
        
        $this->expectException(CartValidationException::class);
        $this->expectExceptionMessage('Please provide a complete shipping address including street, city, state, and zip code.');
        
        $validator->handle($cart, fn($cart) => $cart);
    }

    #[Test]
    public function it_passes_validation_when_cart_is_not_for_delivery_zone(): void
    {
        $user = User::factory()->create();
        $pickup = Pickup::factory()->pickupLocation()->create();
        
        $cart = Cart::factory()->create([
            'shopper_id' => $user->id,
            'pickup_id' => $pickup->id,
        ]);

        // Even with incomplete shipping info, it should pass for pickup locations
        $cart->setShippingInfo([
            'street' => '',
            'city' => '',
            'state' => '',
            'zip' => '',
            'country' => '',
        ]);

        $validator = new HasCompleteShippingAddress();
        
        $result = $validator->handle($cart, fn($cart) => $cart);
        
        $this->assertSame($cart, $result);
    }

    #[Test]
    public function it_passes_validation_when_cart_has_no_location(): void
    {
        $user = User::factory()->create();
        
        $cart = Cart::factory()->create([
            'shopper_id' => $user->id,
            'pickup_id' => null,
        ]);

        $validator = new HasCompleteShippingAddress();
        
        $result = $validator->handle($cart, fn($cart) => $cart);
        
        $this->assertSame($cart, $result);
    }
}
