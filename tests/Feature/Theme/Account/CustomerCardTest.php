<?php

namespace Tests\Feature\Theme\Account;

use App\Actions\Billing\AddSourceToCustomer;
use App\Models\Card;
use App\Models\Order;
use App\Models\Setting;
use App\Models\User;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CustomerCardTest extends TenantTestCase
{
    #[Test]
    public function an_unauthenticated_user_cannot_view_all_cards(): void
    {
        $this->get(route('account.cards.index'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_user_without_an_order_cannot_view_all_of_their_cards(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('account.cards.index'))
            ->assertNotFound();
    }

    #[Test]
    public function a_user_with_an_order_can_view_all_of_their_cards(): void
    {
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true]);

        $expected_cards = Card::factory()->times(2)->create(['user_id' => $user->id]);
        $unexpected_card = Card::factory()->create();

        $this->actingAs($user)
            ->get(route('account.cards.index'))
            ->assertOk()
            ->assertViewIs('theme::customers.cards.index');
    }

    #[Test]
    public function an_unauthenticated_user_cannot_view_add_card_form(): void
    {
        $this->get(route('account.cards.create'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_user_without_an_order_cannot_view_add_card_form(): void
    {
        $this->actingAsCustomer()
            ->get(route('account.cards.create'))
            ->assertNotFound();
    }

    #[Test]
    public function a_user_with_an_order_can_view_stripe_add_card_form(): void
    {
        Setting::updateOrCreate(['key' => 'payment_gateway'], ['value' => 'stripe']);

        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true]);

        $this->actingAs($user)
            ->get(route('account.cards.create'))
            ->assertOk()
            ->assertViewIs('theme::customers.cards.create');
    }

    #[Test]
    public function an_unauthenticated_user_cannot_add_a_new_card(): void
    {
        $this->post(route('account.cards.store'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function it_validates_the_add_new_card_request_for_stripe(): void
    {
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true]);


        $this->actingAs($user)
            ->post(route('account.cards.store'))
            ->assertRedirect()
            ->assertInvalid([
                'token' => 'The token field is required.',
                'cardholder_name' => 'The cardholder name field is required.'
            ]);
    }

    #[Test]
    public function a_user_can_add_their_first_card_for_stripe(): void
    {
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true]);

        $request = [
            'token' => 'tok_123',
            'cardholder_name' => 'First Last'
        ];

        $this->mock(AddSourceToCustomer::class, function (MockInterface $mock) use ($user, $request) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    \Mockery::on(fn(User $arg) => $arg->id === $user->id),
                    'tok_123',
                    $request,
                )
                ->andReturn(Card::factory()->make());
        });

        $this->actingAs($user)
            ->post(route('account.cards.store'), $request)
            ->assertRedirect(route('account.cards.index'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'level' => 'info',
                'message' => 'The card has been added!'
            ]);
    }

    #[Test]
    public function a_user_can_add_another_card_to_stripe(): void
    {
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true]);

        Card::factory()->create(['user_id' => $user->id]);

        $request = [
            'token' => 'tok_123',
            'cardholder_name' => 'First Last'
        ];

        $this->mock(AddSourceToCustomer::class, function (MockInterface $mock) use ($user, $request) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    \Mockery::on(fn(User $arg) => $arg->id === $user->id),
                    'tok_123',
                    $request,
                )
                ->andReturn(Card::factory()->make());
        });

        $this->actingAs($user)
            ->post(route('account.cards.store'), $request)
            ->assertRedirect(route('account.cards.index'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'level' => 'info',
                'message' => 'The card has been added!'
            ]);
    }
}
