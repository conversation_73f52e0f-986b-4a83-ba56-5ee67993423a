<?php

namespace Tests\Feature\Theme\Store;

use App\Events\User\UserWasRegistered;
use App\Models\Integration;
use App\Models\Pickup;
use Illuminate\Routing\Middleware\ThrottleRequestsWithRedis;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Redis;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RegisterTest extends TenantTestCase
{
    #[Test]
    public function it_renders_the_lead_registration_page(): void
    {
        $this->session([
            'postal_code' => '12345',
            'address' => ['city' => 'Test', 'state' => 'State'],
            'options' => []
        ])
            ->get(route('register.lead.show'))
            ->assertViewIs('theme::authentication.register')
            ->assertViewHas('stepView', 'lead')
            ->assertViewHas('postal_code', '12345')
            ->assertViewHas('address', ['city' => 'Test', 'state' => 'State'])
            ->assertViewHas('location_id');
    }

    #[Test]
    public function it_allows_a_user_to_register_as_a_lead(): void
    {
        Carbon::setTestNow($now = now());

        Event::fake([UserWasRegistered::class]);

        $pickup = Pickup::factory()->create();

        $this->post(route('register.lead.store', [
            'email' => '<EMAIL>',
            'password' => '12345678',
            'first_name' => 'First',
            'last_name' => 'Last',
            'username' => '',
            'pickup_point' => $pickup->id,
            'timestamp' => now()->subSeconds(3)->timestamp
        ]))
            ->assertSessionHasNoErrors()
            ->assertRedirect(route('store.index'));

        $this->assertDatabaseHas('leads', [
            'email' => '<EMAIL>',
            'first_name' => 'First',
            'last_name' => 'Last',
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_allows_a_user_to_register_as_a_user(): void
    {
        Carbon::setTestNow($now = now());

        Event::fake([UserWasRegistered::class]);

        $pickup = Pickup::factory()->create();

        $this->post(route('register', [
            'email' => '<EMAIL>',
            'password' => '12345678',
            'first_name' => 'First',
            'last_name' => 'Last',
            'username' => '',
            'pickup_point' => $pickup->id,
            'timestamp' => now()->subSeconds(3)->timestamp
        ]))
            ->assertSessionHasNoErrors()
            ->assertRedirect(route('store.index', ['firstlogin' => $now->timestamp]))
            ->assertSessionHas('userWasCreated', true);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'first_name' => 'First',
            'last_name' => 'Last',
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_rate_throttles_registration_by_minute(): void
    {
        $this->withMiddleware([ThrottleRequestsWithRedis::class]);

        Redis::del(md5('registerregister_daily:127.0.0.1'));
        Redis::del( md5('registerregister_minutely:127.0.0.1'));

        Carbon::setTestNow($now = now());

        Event::fake([UserWasRegistered::class]);

        $pickup = Pickup::factory()->create();

        foreach(range(1,4) as $index) {
            $email = 'test+'.$index.'@gmail.com';
            $this->post(route('register', [
                'email' => $email,
                'password' => '12345678',
                'first_name' => 'First',
                'last_name' => 'Last',
                'username' => '',
                'pickup_point' => $pickup->id,
                'timestamp' => now()->subSeconds(3)->timestamp
            ]))
                ->assertSessionHasNoErrors()
                ->assertRedirect(route('store.index', ['firstlogin' => $now->timestamp]))
                ->assertSessionHas('userWasCreated', true);

            $this->assertDatabaseHas('users', [
                'email' => $email
            ]);
        }

        // should exceed minute limit
        $email = '<EMAIL>';
        $this->post(route('register', [
            'email' => $email,
            'password' => '12345678',
            'first_name' => 'First',
            'last_name' => 'Last',
            'username' => '',
            'pickup_point' => $pickup->id,
            'timestamp' => now()->subSeconds(3)->timestamp
        ]))
            ->assertTooManyRequests();

        $this->assertDatabaseMissing('users', [
            'email' => $email
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_rate_throttles_registration_by_day(): void
    {
        $this->withMiddleware([ThrottleRequestsWithRedis::class]);

        Redis::del(md5('registerregister_daily:127.0.0.1'));

        Carbon::setTestNow($now = now());

        Event::fake([UserWasRegistered::class]);

        $pickup = Pickup::factory()->create();

        foreach(range(1,8) as $index) {
            Redis::del(md5('registerregister_minutely:127.0.0.1'));

            $email = 'test+'.$index.'@gmail.com';
            $this->post(route('register', [
                'email' => $email,
                'password' => '12345678',
                'first_name' => 'First',
                'last_name' => 'Last',
                'username' => '',
                'pickup_point' => $pickup->id,
                'timestamp' => now()->subSeconds(3)->timestamp
            ]))
                ->assertSessionHasNoErrors()
                ->assertRedirect(route('store.index', ['firstlogin' => $now->timestamp]))
                ->assertSessionHas('userWasCreated', true);

            $this->assertDatabaseHas('users', [
                'email' => $email
            ]);
        }

        // should exceed daily limit
        $email = '<EMAIL>';
        $this->post(route('register', [
            'email' => $email,
            'password' => '12345678',
            'first_name' => 'First',
            'last_name' => 'Last',
            'username' => '',
            'pickup_point' => $pickup->id,
            'timestamp' => now()->subSeconds(3)->timestamp
        ]))
            ->assertTooManyRequests();

        $this->assertDatabaseMissing('users', [
            'email' => $email
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_allow_disallowed_email_domain(): void
    {
        $pickup = Pickup::factory()->create();

        $this->post(route('register', [
            'email' => '<EMAIL>',
            'password' => '12345678',
            'first_name' => 'First',
            'last_name' => 'Last',
            'username' => '',
            'pickup_point' => $pickup->id,
            'timestamp' => now()->subSeconds(3)->timestamp
        ]))
            ->assertSessionHasErrors(['email' => 'A valid email is required']);

        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>'
        ]);
    }

    public function it_redirects_to_sms_opt_in_after_registration_when_drip_is_configured_and_enabled(): void
    {
        Event::fake([UserWasRegistered::class]);

        $pickup = Pickup::factory()->create();

        $opt_in_settings = [
            'name' => 'after_registration',
            'enabled' => true,
            'settings' => [
                [
                    'name' => 'message',
                    'value' => 'My message'
                ],
                [
                    'name' => 'button_text',
                    'value' => 'My button'
                ],
                [
                    'name' => 'photo_url',
                    'value' => 'https://example.com/photo.jpeg'
                ]
            ]
        ];

        Integration::factory()->create([
            'name' => 'drip',
            'enabled' => true,
            'settings' => [
                'opt_in_locations' => [$opt_in_settings]
            ]
        ]);

        $this->post(route('register', [
            'email' => '<EMAIL>',
            'password' => '12345678',
            'first_name' => 'First',
            'last_name' => 'Last',
            'pickup_point' => $pickup->id,
        ]))
            ->assertSessionHasNoErrors()
            ->assertRedirect(route('customer.notifications.sms-marketing.show'))
            ->assertSessionHas('userWasCreated', true);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>'
        ]);

        Carbon::setTestNow();
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMiddleware([ThrottleRequestsWithRedis::class]);
    }
}
