<?php

namespace Tests\Feature\Theme\Store;

use App\Models\Pickup;
use App\Models\Setting;
use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ZipGateTest extends TenantTestCase
{
    #[Test]
    public function a_guest_can_browse_store_when_zip_gate_is_disabled(): void
    {
        Setting::updateOrCreate(['key' => 'require_shopper_local'],['value' => false]);

        $this->get(route('store.index'))
            ->assertOk()
            ->assertViewIs('theme::store.index')
            ->assertSessionHas('store_url', request()->fullUrl());
    }

    #[Test]
    public function user_can_browse_store_with_zip_gate_enabled_and_they_have_pickup_point(): void
    {
        Setting::updateOrCreate(['key' => 'require_shopper_local'], ['value' => true]);

        $pickup = Pickup::factory()->create();
        $customer = User::factory()->create(['pickup_point' => $pickup->id]);

        $this->actingAs($customer)
            ->get(route('store.index'))
            ->assertOk();
    }
}
