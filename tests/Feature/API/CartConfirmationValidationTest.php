<?php

namespace Tests\Feature\API;

use App\Models\Cart;
use App\Models\Date;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CartConfirmationValidationTest extends TenantTestCase
{
    #[Test]
    public function it_validates_complete_shipping_address_for_delivery_zones(): void
    {
        $user = User::factory()->create();
        $pickup = Pickup::factory()->deliveryZone()->create();
        $date = Date::factory()->active()->create();
        $product = Product::factory()->create();
        $payment = Payment::factory()->enabled()->create();

        $cart = Cart::factory()->create([
            'shopper_id' => $user->id,
            'pickup_id' => $pickup->id,
        ]);

        $cart->addProduct($product, 1);

        $requestData = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $pickup->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 1,
                ]
            ],
            'customer' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
                'save_for_later' => false,
                'opt_in_to_sms' => false,
            ],
            'shipping' => [
                'street' => '', // Missing required field
                'city' => 'Test City',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'USA',
                'save_for_later' => false,
            ],
            'billing' => [
                'method' => $payment->key,
                'save_for_later' => false,
            ],
        ];

        $this->actingAs($user)
            ->postJson(route('api.cart.confirmation'), $requestData)
            ->assertStatus(409)
            ->assertJson([
                'message' => 'Please provide a complete shipping address including street, city, state, and zip code.'
            ]);
    }

    #[Test]
    public function it_allows_order_confirmation_with_complete_shipping_address(): void
    {
        $user = User::factory()->create();
        $pickup = Pickup::factory()->deliveryZone()->create();
        $date = Date::factory()->active()->create();
        $product = Product::factory()->create();
        $payment = Payment::factory()->enabled()->create();

        $cart = Cart::factory()->create([
            'shopper_id' => $user->id,
            'pickup_id' => $pickup->id,
        ]);

        $cart->addProduct($product, 1);

        $requestData = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $pickup->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 1,
                ]
            ],
            'customer' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
                'save_for_later' => false,
                'opt_in_to_sms' => false,
            ],
            'shipping' => [
                'street' => '123 Main St', // Complete address
                'city' => 'Test City',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'USA',
                'save_for_later' => false,
            ],
            'billing' => [
                'method' => $payment->key,
                'save_for_later' => false,
            ],
        ];

        $this->actingAs($user)
            ->postJson(route('api.cart.confirmation'), $requestData)
            ->assertStatus(201)
            ->assertJsonStructure(['order']);
    }

    #[Test]
    public function it_does_not_validate_shipping_address_for_pickup_locations(): void
    {
        $user = User::factory()->create();
        $pickup = Pickup::factory()->pickupLocation()->create();
        $date = Date::factory()->active()->create();
        $product = Product::factory()->create();
        $payment = Payment::factory()->enabled()->create();

        $cart = Cart::factory()->create([
            'shopper_id' => $user->id,
            'pickup_id' => $pickup->id,
        ]);

        $cart->addProduct($product, 1);

        $requestData = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $pickup->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 1,
                ]
            ],
            'customer' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
                'save_for_later' => false,
                'opt_in_to_sms' => false,
            ],
            'shipping' => [
                'street' => '', // Empty address should be fine for pickup
                'city' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'save_for_later' => false,
            ],
            'billing' => [
                'method' => $payment->key,
                'save_for_later' => false,
            ],
        ];

        $this->actingAs($user)
            ->postJson(route('api.cart.confirmation'), $requestData)
            ->assertStatus(201)
            ->assertJsonStructure(['order']);
    }
}
