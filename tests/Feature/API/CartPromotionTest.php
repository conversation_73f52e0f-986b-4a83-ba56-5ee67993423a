<?php

namespace Tests\Feature\API;

use App\Actions\Order\ApplyCoupon;
use App\Contracts\CartService;
use App\Models\Cart;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\User;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CartPromotionTest extends TenantTestCase
{
    #[Test]
    public function a_customer_must_be_logged_in_to_add_a_promotion(): void
    {
        $coupon = Coupon::factory()->create();

        $this->mock(ApplyCoupon::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->postJson(route('api.carts.promotions.store'), [
            'code' => $coupon->code
        ])
            ->assertUnauthorized();
    }

    #[Test]
    public function a_valid_cart_is_required_to_add_a_promotion(): void
    {
        $coupon = Coupon::factory()->create();

        $this->mock(ApplyCoupon::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->actingAsApiCustomer()
            ->postJson(route('api.carts.promotions.store'), [
                'code' => $coupon->code
            ])
            ->assertStatus(409);
    }

    #[Test]
    public function it_validates_the_promo_code_request(): void
    {
        $user = User::factory()->create();

        $this->mock(CartService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('find');
        });

        $this->mock(ApplyCoupon::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.promotions.store'))
            ->assertUnprocessable()
            ->assertJsonFragment(['code' => ['The code field is required.']]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.promotions.store'), [
                'code' => 'some_invalid_code'
            ])
            ->assertUnprocessable()
            ->assertJsonFragment(['code' => ['The selected code is invalid.']]);
    }

    #[Test]
    public function it_can_add_a_promotion_code_to_a_database_cart(): void
    {
        $user = User::factory()->create();

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $this->mock(CartService::class, function (MockInterface $mock) use ($user, $cart) {
            $mock->shouldReceive('find')->once()
                ->with(User::class, $user->id)->andReturn($cart);
        });

        $coupon = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 1000]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.promotions.store'), [
                'code' => $coupon->code
            ])
            ->assertCreated()
            ->assertJsonStructure([
                'code',
                'name',
                'amount',
                'message',
            ])
            ->assertJsonFragment([
                'amount' => 1000,
            ]);
    }

    #[Test]
    public function it_can_add_a_promotion_code_to_an_order_cart(): void
    {
        $user = User::factory()->create();

        $order = Order::factory()->create(['customer_id' => $user->id]);

        $this->mock(CartService::class, function (MockInterface $mock) use ($user, $order) {
            $mock->shouldReceive('find')->once()
                ->with(User::class, $user->id)->andReturn($order);
        });

        $coupon = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 1000]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.promotions.store'), [
                'code' => $coupon->code
            ])
            ->assertCreated()
            ->assertJsonStructure([
                'code',
                'name',
                'amount',
                'message',
            ])
            ->assertJsonFragment([
                'amount' => 1000,
            ]);
    }
}