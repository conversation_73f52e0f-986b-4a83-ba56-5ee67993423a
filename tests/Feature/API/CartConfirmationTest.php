<?php

namespace Tests\Feature\API;

use App\Actions\Cart\ConfirmDatabaseCartWithCart;
use App\Events\Order\OrderWasConfirmed;
use App\Events\Subscription\CustomerSubscribed;
use App\Events\Subscription\RecurringOrderCreated;
use App\Models\Card;
use App\Models\Cart;
use App\Models\Coupon;
use App\Models\Date;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CartConfirmationTest extends TenantTestCase
{
    #[Test]
    public function it_requires_a_logged_in_user(): void
    {
        $this->postJson(route('api.carts.confirm'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_validates_the_cart_confirmation_request(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'))
            ->assertUnprocessable()
            ->assertJsonFragment([
                'type' => ['The type field is required.'],
                'id' => ['The id field is required.'],
                'purchase_type' => ['The purchase type field is required.'],
                'delivery_method_id' => ['The delivery method id field is required.'],
                'items' => ['The items field is required.'],
                'customer' => ['The customer field is required.'],
                'billing' => ['The billing field is required.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), [
                'type' => 'foo',
                'purchase_type' => 'foo',
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'type' => ['The selected type is invalid.'],
                'purchase_type' => ['The selected purchase type is invalid.'],
            ]);

        $cart = Cart::factory()->create();
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), [
                'type' => 'database',
                'id' => $cart->id,
            ])
            ->assertUnprocessable() // doesnt belong to auth user
            ->assertJsonFragment(['id' => ['The selected id is invalid.']]);

        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => false]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['date_id' => $date->id])
            ->assertUnprocessable() // inactive date
            ->assertJsonFragment(['date_id' => ['The selected date id is invalid.']]);

        $location = Pickup::factory()->create(['status_id' => 2]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['delivery_method_id' => $location->id,])
            ->assertUnprocessable() // closed location
            ->assertJsonFragment(['delivery_method_id' => ['The selected delivery method id is invalid.']]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), [
                'items' => 'foo',
                'subscription' => 'foo',
                'customer' => 'foo',
                'shipping' => 'foo',
                'billing' => 'foo',
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items' => ['The items field must be an array.'],
                'subscription' => ['The subscription field must be an array.'],
                'customer' => ['The customer field must be an array.'],
                'shipping' => ['The shipping field must be an array.'],
                'billing' => ['The billing field must be an array.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['items' => []])
            ->assertUnprocessable()
            ->assertJsonFragment(['items' => ['The items field is required.']]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['items' => [[]]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items.0.product_id' => ['The items.0.product_id field is required.'],
                'items.0.quantity' => ['The items.0.quantity field is required.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['items' => [[
                'product_id' => 'foo',
                'quantity' => 0,
            ]]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items.0.product_id' => ['The selected items.0.product_id is invalid.'],
                'items.0.quantity' => ['The items.0.quantity field must be at least 1.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['purchase_type' => 'recurring'])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'subscription' => ['The subscription field is required when purchase type is recurring.'],
                'subscription.frequency' => ['The subscription.frequency field is required when purchase type is recurring.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), [
                'purchase_type' => 'recurring',
                'subscription' => ['product_incentive_id' => 'foo']
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'subscription.product_incentive_id' => ['The selected subscription.product incentive id is invalid.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['customer' => [
                'save_for_later' => 'foo',
                'opt_in_to_sms' => 'foo',
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'customer.first_name' => ['The customer.first name field is required.'],
                'customer.last_name' => ['The customer.last name field is required.'],
                'customer.email' => ['The customer.email field is required.'],
                'customer.phone' => ['The customer.phone field is required.'],
                'customer.save_for_later' => ['The customer.save for later field must be true or false.'],
                'customer.opt_in_to_sms' => ['The customer.opt in to sms field must be true or false.'],
            ]);

        $method = Payment::factory()->create(['enabled' => false]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['billing' => [
                'method' => $method->key,
                'save_for_later' => 'foo',
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'billing.method' => ['The selected billing.method is invalid.'],
                'billing.save_for_later' => ['The billing.save for later field must be true or false.'],
            ]);

        $method = Payment::factory()->create(['enabled' => true, 'key' => 'other']);
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['billing' => [
                'method' => $method->key,
                'source_id' => null
            ]])
            ->assertUnprocessable()
            ->assertJsonMissingValidationErrors([
                'billing.source_id'
            ]);

        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['billing' => [
                'method' => $method->key,
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'billing.source_id' => ['The billing.source id field is required when billing.method is card.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'),[
                'notes' => Str::random(1001),
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'notes' => ['The notes field must not be greater than 1000 characters.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'),[
                'is_gift' => 'true',
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'recipient_email' => ['The recipient email field is required when is gift is accepted.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'),[
                'recipient_email' => 'abc',
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'recipient_email' => ['The recipient email field must be a valid email address.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'),[
                'recipient_notes' => Str::random(501),
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'recipient_notes' => ['The recipient notes field must not be greater than 500 characters.'],
            ]);
    }

    #[Test]
    public function a_user_cannot_confirm_a_cart_that_does_not_meet_product_minimum(): void
    {
        $pickup = Pickup::factory()->create(['schedule_id' => null]);

        $cart = Cart::factory()->create();

        $cart->updateCartLocation($pickup);

        $product = Product::factory()->create(['settings' => ['order_minimum' => 100000]]);
        $cart->addProduct($product, 2);

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => null,
            'delivery_method_id' => $pickup->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => Payment::factory()->create()->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $this->actingAs($cart->cartCustomer(), 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertConflict()
            ->assertJsonFragment(['message' => 'The cart total must be at least $100,000 to purchase '. $product->title]);
    }

    #[Test]
    public function a_user_cannot_confirm_a_cart_that_does_not_meet_delivery_method_minimum(): void
    {
        $pickup = Pickup::factory()->create(['min_customer_orders' => '100.01', 'schedule_id' => null]); // $100.01

        $user = User::factory()->create(['pickup_point' => $pickup->id]);

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $cart->updateCartLocation($pickup);

        $product = Product::factory()->create(['unit_price' => 1]);
        $cart->addProduct($product, 2);

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => null,
            'delivery_method_id' => $pickup->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => Payment::factory()->create()->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $this->actingAs($cart->cartCustomer(), 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertConflict()
            ->assertJsonFragment(['message' => 'The subtotal must be at least $100.01 before an order can be placed.']);
    }

    #[Test]
    public function a_user_cannot_confirm_a_cart_with_a_pickup_schedule_without_a_date(): void
    {
        $payment = Payment::factory()->create(['enabled' => true, 'key' => 'other']);

        $schedule = Schedule::factory()->create();

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id, 'payment_methods' => [$payment->id]]);

        $cart = Cart::factory()->create();

        $cart->updateCartLocation($pickup);

        $product = Product::factory()->create();
        $cart->addProduct($product, 2);

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => null,
            'delivery_method_id' => $pickup->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => Payment::factory()->create()->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $this->actingAs($cart->cartCustomer(), 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertConflict()
            ->assertJsonFragment(['message' => 'Ordering is currently closed for On Farm Pickup']);
    }

    #[Test]
    public function a_user_cannot_confirm_a_cart_outside_their_delivery_zone(): void
    {
        Setting::updateOrCreate(['key' => 'require_checkout_agreement'], ['value' => 1]);

        $payment = Payment::factory()->create(['enabled' => true, 'key' => 'other']);

        $pickup = Pickup::factory()->create([
            'schedule_id' => null,
            'fulfillment_type' => 2,
            'payment_methods' => [$payment->id]
        ]);

        $cart = Cart::factory()->create();

        $cart->updateCartLocation($pickup);

        $product = Product::factory()->create();
        $cart->addProduct($product, 2);

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => null,
            'delivery_method_id' => $pickup->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => '',
                'city' => 'City',
                'state' => 'ST',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => Payment::factory()->create()->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $this->actingAs($cart->cartCustomer(), 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertConflict()
            ->assertJsonFragment(['message' => 'Please provide an address that is covered by the selected delivery method.']);
    }

    #[Test]
    public function it_confirms_a_one_time_order_delivery_with_a_cart(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $cart->updateCartLocation($location);

        $product = Product::factory()->create();
        $cart->addProduct($product);

        $method = Payment::factory()->create();

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $response = $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertCreated()
            ->json('order');

        $this->assertDatabaseHas(Order::class, [
            'id' => $response['id'],
            'confirmed' => 1,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => '123 Test St',
            'shipping_street_2' => 'Apt Test',
            'shipping_city' => 'Test',
            'shipping_state' => 'TE',
            'shipping_zip' => '12345',
            'shipping_country' => 'US',
            'payment_id' => $method->id
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_confirms_a_subscription_order_with_a_cart(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class, CustomerSubscribed::class]);

        $user = User::factory()->create();
        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7,14]]);
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => $schedule->id, 'active' => true]);
        $next_date = Date::factory()->create(['schedule_id' => $schedule->id, 'active' => true, 'pickup_date' => today()->addDays(28)]);

        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $schedule->id]);

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $promo = Product::factory()->create();

        $cart->updateCartLocation($location);
        $cart->setCartAsSubscriptionPurchase(7, $promo->id);

        $product = Product::factory()->create();
        $cart->addProduct($product);

        $method = Payment::factory()->create();

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'recurring',
            'subscription' => [
                'frequency' => 7,
                'product_incentive_id' => $promo->id,
            ],
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($promo) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldReceive('hasProductIncentive')->andReturnTrue();
            $mock->shouldReceive('defaultProductIncentiveId')->andReturn($promo->id);
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
            $mock->shouldReceive('inventoryManagementDayCount')->andReturn(3);
        });

        $response = $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertCreated()
            ->json('order');

        $this->assertDatabaseHas(Order::class, [
            'id' => $response['id'],
            'confirmed' => 1,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => '123 Test St',
            'shipping_street_2' => 'Apt Test',
            'shipping_city' => 'Test',
            'shipping_state' => 'TE',
            'shipping_zip' => '12345',
            'shipping_country' => 'US',
            'payment_id' => $method->id
        ]);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'customer_id' => $user->id,
            'reorder_frequency' => 7,
            'fulfillment_id' => $location->id,
            'generate_at' => $next_date->order_end_date->subDays(3)->setTime(23,59,59),
            'ready_at' => $next_date->pickup_date->setTime(0,0,0)
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'type' => 'promo',
            'product_id' => $promo->id
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertDispatched(RecurringOrderCreated::class);
        Event::assertDispatched(CustomerSubscribed::class);
    }

    #[Test]
    public function it_confirms_a_one_time_order_pickup_with_a_cart(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $cart->updateCartLocation($location);

        $product = Product::factory()->create();
        $cart->addProduct($product);

        $method = Payment::factory()->create();

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'subscription' => null,
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => null,
                'street_2' => null,
                'city' => null,
                'state' => '',
                'zip' => '',
                'country' => '',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $response = $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertCreated()
            ->json('order');

        $this->assertDatabaseHas(Order::class, [
            'id' => $response['id'],
            'confirmed' => 1,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => null,
            'shipping_street_2' => '',
            'shipping_city' => null,
            'shipping_state' => null,
            'shipping_zip' => null,
            'shipping_country' => null,
            'payment_id' => $method->id
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_confirms_a_one_time_order_pickup_without_a_date_with_a_cart(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => null]);

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $cart->updateCartLocation($location);

        $product = Product::factory()->create();
        $cart->addProduct($product);

        $method = Payment::factory()->create();

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'subscription' => null,
            'date_id' => null,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $response = $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertCreated()
            ->json('order');

        $this->assertDatabaseHas(Order::class, [
            'id' => $response['id'],
            'confirmed' => 1,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => '123 Test St',
            'shipping_street_2' => 'Apt Test',
            'shipping_city' => 'Test',
            'shipping_state' => 'TE',
            'shipping_zip' => '12345',
            'shipping_country' => 'US',
            'payment_id' => $method->id
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_confirms_a_one_time_order_with_a_coupon(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $method = Payment::factory()->create();

        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $cart->updateCartLocation($location);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $cart->addProduct($product, 2);

        $coupon = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 100]);

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'subscription' => null,
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'discounts' => [
                'coupons' => [
                    ['code' => $coupon->code, 'name' => $coupon->description, 'amount' => '1234']
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $response = $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertCreated()
            ->json('order');

        $this->assertDatabaseHas(Order::class, [
            'id' => $response['id'],
            'confirmed' => 1,
            'total' => 1900,
            'coupon_subtotal' => 100
        ]);

        $this->assertDatabaseHas('coupon_order', [
            'coupon_id' => $coupon->id,
            'order_id' => $response['id'],
            'savings' => 100
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_updates_profile_when_using_saving_for_later(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $old_card = Card::factory()->create(['user_id' => $user->id, 'default' => true]);
        $card = Card::factory()->create(['user_id' => $user->id, 'default' => false]);

        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $cart->updateCartLocation($location);

        $product = Product::factory()->create();
        $cart->addProduct($product);

        $method = Payment::where(['key' => 'card'])->first();
        $method->enabled = true;
        $method->save();

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertCreated();

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'first_name' => 'First test',
            'last_name' => 'Last test',
            'email' => $user->email,
            'phone' => '************',
            'street' => '123 Test St',
            'street_2' => 'Apt Test',
            'city' => 'Test',
            'state' => 'TE',
            'zip' => '12345',
            'checkout_card_id' => $card->id
        ]);

        $this->assertDatabaseHas(Card::class, [
            'id' =>  $old_card->id,
            'default' => 0,
        ]);

        $this->assertDatabaseHas(Card::class, [
            'id' =>  $card->id,
            'default' => 1,
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_doesnt_update_profile_when_not_saving_for_later(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id, 'default' => false]);

        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $cart = Cart::factory()->create(['shopper_id' => $user->id, 'shopper_type' => User::class]);

        $cart->updateCartLocation($location);

        $product = Product::factory()->create();
        $cart->addProduct($product);

        $method = Payment::factory()->create();

        $request_body = [
            'type' => 'database',
            'id' => $cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => false,
                'opt_in_to_sms' => false,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => false
            ],
        ];

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $request_body)
            ->assertCreated();

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'street' => $user->street,
            'street_2' => $user->street_2,
            'city' => $user->city,
            'state' => $user->state,
            'zip' => $user->zip,
            'checkout_card_id' => $user->checkout_card_id
        ]);

        $this->assertDatabaseHas(Card::class, [
            'id' =>  $card->id,
            'default' => 0,
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_confirms_a_gift_order(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $method = Payment::factory()->create();

        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);

        $product = Product::factory()->create();
        $db_cart->addProduct($product, 2);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'is_gift' => true,
            'recipient_email' => '<EMAIL>',
            'recipient_notes' => 'These are gift notes',
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => null,
                'save_for_later' => true
            ],
        ];

        $order = Order::factory()->create();

        $this->mock(ConfirmDatabaseCartWithCart::class, function (MockInterface $mock) use ($order, $db_cart, $cart) {
            $mock->shouldReceive('handle')
                ->once()
                ->andReturn($order);
        });

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated()
            ->assertJson(['order' => ['id' => $order->id]]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}
