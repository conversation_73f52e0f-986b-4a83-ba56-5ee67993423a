<?php

namespace Tests\Feature\Admin;

use App\Models\Product;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductDescriptionControllerTest extends TenantTestCase
{
    #[Test]
    public function it_updates_product_description_summary_ingredients(): void
    {
        $product = Product::factory()->create([
            'title' => 'Lean Steak',
            'slug' => 'lean-steak',
            'unit_price' => '7.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345',
            'fulfillment_instructions' => 'Fulfillment Instructions',
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->put(route('admin.products.description.update', compact('product')), [
            'description' => 'Lean Steak Description',
            'summary' => 'Lean Steak Summary',
            'ingredients' => 'Lean Steak Ingredients',
            'fulfillment_instructions' => 'Lean Steak Fulfillment Instructions',
        ])
            ->assertRedirect(route('admin.products.edit', compact('product')));

        $this->assertDatabaseHas('products', [
            'description' => 'Lean Steak Description',
            'summary' => 'Lean Steak Summary',
            'ingredients' => 'Lean Steak Ingredients',
            'fulfillment_instructions' => 'Lean Steak Fulfillment Instructions',
        ]);
    }
}
