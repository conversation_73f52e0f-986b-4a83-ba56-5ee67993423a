<?php

namespace Tests\Feature\Admin;

use App\Models\Setting;
use App\Models\Template;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GiftPurchasedSettingsNotificationTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_update_notification_settings(): void
    {
        $this->put(route('admin.settings.update'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_update_notification_settings(): void
    {
        $this->actingAsCustomer()
            ->put(route('admin.settings.update'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_update_gift_purchased_notification_template(): void
    {
        $template = Template::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.settings.edit', ['setting' => 'notifications']))
            ->assertOk();

        $this->put(route('admin.settings.update'), [
                'settings' => ['email_gift_purchased_template' => $template->id]
            ])
            ->assertSessionDoesntHaveErrors()
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'notifications']));

        $this->assertDatabaseHas(Setting::class, [
            'key' => 'email_gift_purchased_template',
            'value' => $template->id
        ]);
    }

    #[Test]
    public function an_admin_can_update_gift_purchased_notification_template_to_null(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.settings.edit', ['setting' => 'notifications']))
            ->assertOk();

        $this->put(route('admin.settings.update'), [
                'settings' => ['email_gift_purchased_template' => '']
            ])
            ->assertSessionDoesntHaveErrors()
            ->assertRedirect(route('admin.settings.edit', ['setting' => 'notifications']));

        $this->assertDatabaseHas(Setting::class, [
            'key' => 'email_gift_purchased_template',
            'value' => null
        ]);
    }

    #[Test]
    public function it_validates__when_updating_gift_purchased_notification_template(): void
    {
        $template = Template::factory()->create();

        $this->actingAsAdmin()
           ->put(route('admin.settings.update'), [
                'settings' => ['email_gift_purchased_template' => 'AC']
            ])
            ->assertSessionHasErrors(['settings.email_gift_purchased_template' => 'The settings.email gift purchased template field must be an integer.']);

        $this->assertDatabaseMissing(Setting::class, [
            'key' => 'email_gift_purchased_template',
            'value' => 'AC'
        ]);
    }
}
