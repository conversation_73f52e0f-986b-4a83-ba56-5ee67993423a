<?php

namespace Tests\Feature\Admin;

use App\Jobs\SendLastChanceEmail;
use App\Models\Schedule;
use App\Models\Template;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ScheduleReminderTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_send_schedule_reminders(): void
    {
        $schedule = Schedule::factory()->create();

        $this->post(route('admin.schedules.reminders.store', compact('schedule')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function non_admins_cannot_send_schedule_reminders(): void
    {
        $schedule = Schedule::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.schedules.reminders.store', compact('schedule')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function admins_cannot_send_schedule_reminders_for_invalid_schedules(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.schedules.reminders.store', ['abc']))
            ->assertRedirect('/');
    }

    #[Test]
    public function it_validates_the_schedule_reminder_request(): void
    {
        $schedule = Schedule::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.schedules.reminders.store', compact('schedule')))
            ->assertRedirect('/')
            ->assertInvalid(['template_id' => 'The template id field is required.']);

        $this->post(route('admin.schedules.reminders.store', compact('schedule')), [
            'template_id' => 'abc'
        ])
            ->assertRedirect('/')
            ->assertInvalid(['template_id' => 'The template id field must be an integer.']);

        $this->post(route('admin.schedules.reminders.store', compact('schedule')), [
                'template_id' => '91239123123'
            ])
            ->assertRedirect('/')
            ->assertInvalid(['template_id' => 'The selected template id is invalid.']);
    }

    #[Test]
    public function admins_cannot_send_schedule_reminders_for_schedules_without_an_upcoming_date(): void
    {
        $schedule = Schedule::factory()->create();
        $template = Template::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.schedules.reminders.store', compact('schedule')), [
                'template_id' => (string) $template->id
            ])
            ->assertRedirect('/')
            ->assertSessionHas('flash_notification', [
                'message' => 'This schedule has no upcoming dates.',
                'level' => 'error'
            ]);
    }

    #[Test]
    public function admins_can_send_schedule_reminders(): void
    {
        Bus::fake([SendLastChanceEmail::class]);

        $schedule = Schedule::factory()->hasDates()->create();
        $template = Template::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.schedules.reminders.store', compact('schedule')), [
                'template_id' => (string) $template->id
            ])
            ->assertRedirect('/')
            ->assertSessionHas('flash_notification', [
                'message' => 'Notifications have been sent.',
                'level' => 'info'
            ]);

        Bus::assertDispatched(
            SendLastChanceEmail::class,
            fn(SendLastChanceEmail $job) =>
                $job->scheduleId === $schedule->id
                    && $job->templateId === $template->id
        );
    }
}