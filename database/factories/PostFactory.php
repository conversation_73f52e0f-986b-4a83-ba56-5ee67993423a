<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PostFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'title' => $title = $this->faker->unique()->md5(),
            'slug' => Str::slug($title),
            'body' => $this->faker->sentence(),
            'summary' => $this->faker->sentence(),
            'cover_photo' => $this->faker->imageUrl(),
            'hidden' => false,
            'visits' => 15,
            'status' => 'new', //Todo: new?  $table->enum('status', ['published','draft'])->default('draft');
            'settings' => [],
            'published_year' => now()->format('Y'),
            'published_month' => now()->format('m'),
            'published_day' => now()->format('d'),
            'published_at' => now(),
        ];
    }
}
