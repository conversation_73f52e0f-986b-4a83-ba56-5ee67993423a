<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Pickup;
use App\Models\PickupState;

class PickupStateFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'pickup_id' => Pickup::factory(),
            'state' => $this->faker->stateAbbr(),
        ];
    }
}
