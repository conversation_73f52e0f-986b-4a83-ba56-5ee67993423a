<?php

namespace Database\Factories;

use App\Models\Schedule;
use App\Support\Enums\PickupStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PickupFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'title' => 'On Farm Pickup',
            'slug' => Str::slug($this->faker->unique()->md5()),
            'subtitle' => $this->faker->word(),
            'description' => $this->faker->paragraph(),
            'fulfillment_type' => 1,
            'delivery_rate' => 0,
            'tax_rate' => 0.07,
            'street' => $this->faker->streetAddress(),
            'street_2' => '',
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'country' => 'USA',
            'zip' => $this->faker->postcode(),
            'contact_phone' => $this->faker->phoneNumber(),
            'lat' => $this->faker->latitude(),
            'lng' => $this->faker->longitude(),
            'visible' => true,
            'status_id' => PickupStatus::open(),
            'apply_limit' => false,
            'delivery_total_threshold' => 0,
            'display_cart_shipping_calculator' => 0,
            'delivery_fee_cap' => 0,
            'tax_delivery_fee' => false,
            'min_customer_orders' => 0,
            'payment_methods' => ["1", "2"],
            'stripe_terminal_location_id' => null,
            'schedule_id' => Schedule::factory(),
            'discount' => $this->faker->randomNumber(),
            'credit_type' => $this->faker->randomElement(['none','referral','percent']),
            'min_combined_orders' => 0,
        ];
    }

    public function delivery()
    {
        return $this->state(function () {
            return ['fulfillment_type' => 2];
        });
    }

    public function withRepeatingSchedule()
    {
        return $this->state(fn (array $attributes): array => [
            'schedule_id' => Schedule::factory()->hasDate()->create(['type_id' => Schedule::TYPE_REPEATING])
        ]);
    }

    public function withCustomSchedule()
    {
        return $this->state(fn (array $attributes): array => [
            'schedule_id' => Schedule::factory()->hasDate()->create(['type_id' => Schedule::TYPE_CUSTOM])
        ]);
    }
}
