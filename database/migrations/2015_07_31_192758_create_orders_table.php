<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->increments('id');
            $table->string('tracking_id')->nullable();
            $table->integer('type_id')->unsigned()->default(1);
            $table->integer('order_number');
            $table->integer('customer_id')->unsigned()->index();
            $table->integer('staff_id')->unsigned();
            $table->string('accounting_id')->nullable();

            $table->integer('total');
            $table->integer('payments_subtotal')->unsigned()->nullable();
            $table->integer('original_total');

            $table->integer('subtotal')->unsigned();
            $table->integer('original_subtotal')->unsigned();

            $table->integer('tax')->unsigned();
            $table->integer('original_tax')->unsigned();
            $table->string('tax_rate');

            $table->decimal('weight', 12, 3);
            $table->decimal('original_weight', 12, 3);

            $table->integer('order_discount')->unsigned();
            $table->integer('coupon_subtotal')->unsigned();
            $table->enum('discount_type', ['fixed','percent']);

            $table->integer('fees_subtotal')->unsigned();
            $table->integer('original_fees_subtotal')->unsigned();

            $table->integer('credit_applied')->unsigned();

            $table->integer('delivery_rate')->unsigned();
            $table->tinyInteger('delivery_fee_type')->default(1);
            $table->integer('delivery_fee')->unsigned();

            $table->integer('schedule_id')->unsigned()->index();
            $table->integer('pickup_id')->unsigned()->index();
            $table->tinyInteger('status_id')->unsigned()->default(1)->index();

            $table->date('deadline_date')->nullable();
            $table->date('pickup_date')->nullable();
            $table->date('original_pickup_date')->nullable();

            $table->string('customer_first_name');
            $table->string('customer_last_name');
            $table->string('customer_email');
            $table->string('customer_email_alt')->nullable();
            $table->string('customer_phone');
            $table->string('customer_phone_2');

            $table->string('shipping_street');
            $table->string('shipping_street_2');
            $table->string('shipping_city');
            $table->string('shipping_state');
            $table->string('shipping_zip');
            $table->string('shipping_country')->nullable();

            $table->string('billing_street');
            $table->string('billing_street_2');
            $table->string('billing_city');
            $table->string('billing_state');
            $table->string('billing_zip');
            $table->string('billing_country')->nullable();

            $table->text('customer_notes');
            $table->text('packing_notes');
            $table->text('invoice_notes');
            $table->text('payment_notes');

            $table->boolean('flagged')->default(false);
            $table->boolean('fulfillment_error')->default(false);
            $table->boolean('packed')->default(false);
            $table->boolean('canceled')->default(false);
            $table->boolean('confirmed')->default(false);
            $table->date('confirmed_date')->nullable();
            $table->date('due_date')->nullable();
            $table->string('payment_terms')->nullable();
            $table->boolean('processed')->default(false);
            $table->date('processed_date')->nullable();

            $table->integer('payment_id')->unsigned();
            $table->integer('payment_source_id')->unsigned()->nullable();
            $table->boolean('paid')->default(false);
            $table->timestamp('payment_date')->nullable();

            $table->integer('containers')->unsigned();
            $table->integer('containers_2');
            $table->boolean('exported')->default(false);
            $table->boolean('picked_up')->default(false);
            $table->integer('last_modified_by')->unsigned();
            $table->boolean('first_time_order')->default(false);
            $table->unsignedSmallInteger('created_year');
            $table->unsignedTinyInteger('created_month');
            $table->unsignedTinyInteger('created_day');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('orders');
    }
};
