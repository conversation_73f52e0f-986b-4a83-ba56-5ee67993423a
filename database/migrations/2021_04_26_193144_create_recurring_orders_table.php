<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recurring_orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('customer_id')->unsigned()->index();
            $table->bigInteger('fulfillment_id')->unsigned()->nullable()->index();
            $table->bigInteger('schedule_id')->unsigned()->nullable()->index();
            $table->integer('reorder_frequency')->unsigned()->nullable();
            $table->integer('skip_count')->unsigned()->default(0);
            $table->dateTime('paused_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recurring_orders');
    }
};
